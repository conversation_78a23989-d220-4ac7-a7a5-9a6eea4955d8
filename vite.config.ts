/// <reference types="vitest/config" />

import { vitePlugin as remix } from "@remix-run/dev";
import { installGlobals } from "@remix-run/node";
import { visualizer } from "rollup-plugin-visualizer";
import { loadEnv } from "vite";
import { imagetools } from "vite-imagetools";
import lqip from "vite-plugin-lqip";
import tsConfigPaths from "vite-tsconfig-paths";
import { defineConfig, mergeConfig, ViteUserConfig } from "vitest/config";
import devtoolsJson from "vite-plugin-devtools-json";
import mkcert from "vite-plugin-mkcert";
import { flatRoutes } from "remix-flat-routes";
import { configDefaults } from "vitest/config";

installGlobals();

export default defineConfig((config) => {
  const { mode, isSsrBuild, command } = config;
  // poluate process.env with env file during build time
  const env = loadEnv(mode, "./app/envs");
  process.env = { ...process.env, ...env };
  const isDevelopment = mode === "development";
  const withRemix = !process.env.VITEST;
  return mergeConfig(
    {
      envDir: "./app/envs",
      ssr: {
        noExternal: ["date-fns"],
      },
      base: process.env.VITE_SSR_STATIC_PATH,
      server: {
        host: true,
        port: parseInt(process.env.VITE_PORT || "5173"),
      },
      publicDir: command === "serve" ? "public" : false,
      build: {
        target: isSsrBuild ? ["node20"] : undefined,
        sourcemap: isSsrBuild || mode === "dev",
      },
      plugins: [
        isDevelopment &&
          mkcert({
            savePath: "./cert",
          }),
        devtoolsJson(),
        lqip(),
        imagetools(),
        tsConfigPaths(),
        isDevelopment &&
          visualizer({
            emitFile: true,
          }),
      ],
      test: {
        globals: true,
        environment: "jsdom",
        setupFiles: ["./test/setup.ts"],
        exclude: [...configDefaults.exclude, "playwright/**"],
        coverage: {
          reporter: ["text", "json", "html"],
          thresholds: {
            statements: 80,
            branches: 80,
            functions: 80,
            lines: 80,
          },
          include: ["app/**/*.{ts,tsx}"],
          exclude: [
            "app/entry.client.tsx",
            "app/entry.server.tsx",
            "app/root.tsx",
            "app/routes/**",
            "app/mocks/**",
            "app/public/**",
            "app/theme/**",
            "app/utils/logging.ts",
          ],
        },
      },
    } as ViteUserConfig,
    {
      plugins: withRemix
        ? [
            remix({
              routes(defineRoutes) {
                return flatRoutes("routes", defineRoutes, {
                  ignoredRouteFiles: ["**/.*"], // Ignore dot files (like .DS_Store)
                  //appDir: 'app',
                  //routeDir: 'routes',
                  //basePath: '/',
                  //paramPrefixChar: '$',
                  //nestedDirectoryChar: '+',
                  //routeRegex: /((\${nestedDirectoryChar}[\/\\][^\/\\:?*]+)|[\/\\]((index|route|layout|page)|(_[^\/\\:?*]+)|([^\/\\:?*]+\.route)))\.(ts|tsx|js|jsx|md|mdx)$$/,
                });
              },
            }),
          ]
        : [],
    },
  );
});
