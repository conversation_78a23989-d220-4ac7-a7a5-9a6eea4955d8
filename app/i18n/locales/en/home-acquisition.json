{"app": {"title": "A home designed for you"}, "pages": {"faqs": {"header": "Frequently Asked Questions", "searchTitle": "Search Results:", "loadingTitle": "Loading frequently asked questions", "noResultsTitle": "No results found", "searchPlaceholder": "Search", "loadingDescription": " Please wait", "noResultsDescription": "No matching results found. Please refine your search with different keywords or check our FAQs and user guides for more information."}, "error": {"contactUs": "Contact us", "somethingWrong": "Something went wrong...Please try again or contact us.", "propertyOnHoldBtn": "RETURN TO MASTERPLAN", "propertyOnHoldDesc": "Another customer began reserving this unit before you. Please check back later or explore other units.", "propertyOnHoldTitle": "Sorry, this unit is no longer available"}, "faqsV2": {"faqs": "FAQs", "title": "Have a question?", "userGuide": "User guides", "minuteRead": "{{count}} MINUTES READ", "description": "Get answers fast in our FAQs", "contactUsBtn": "CONTACT US", "searchPlaceholder": "Search"}, "signUp": {"title": "Please sign-up or log-in by entering your mobile phone number", "description": "Discover the perfect home in Saudi Arabia with Roshn Real Estate. Explore our diverse property listings and find your dream residence. Sign up to Roshn for exclusive access to the latest listings, expert advice, and personalized property recommendations. Your journey to a new home starts here – sign up today and discover our latest resdential projects in Riyadh and Jeddah"}, "profile": {"verify": "VERIFY MY ACCOUNT", "myAccount": "My Account", "accountTabs": {"personalInformation": "Personal information"}, "emailVerified": "<PERSON><PERSON>", "addressDetails": {"city": "City", "title": "address", "district": "District", "postCode": "Post Code", "regionName": "Region", "streetName": "Street", "buildingNumber": "Building Number", "primaryAddress": "Primary Address"}, "nafathVerified": "<PERSON><PERSON><PERSON>", "editEmailDialog": {"title": "Change your email", "newEmail": "Please enter your new email"}, "emailUnverified": "Email Unverified", "personalDetails": {"title": "personal details", "lastName": "Last name", "firstName": "First name", "phoneNumber": "Phone number"}, "nafathUnverified": "Nafath Unverified", "accountVerification": "Account Verification", "emailAddressDetails": {"cta": "EDIT", "email": "E-mail", "title": "e-mail address"}, "accountActionsDetails": {"cta": "RESET PASSWORD", "title": "account actions", "currentPassword": "Current Password", "passwordPlaceholder": "*******"}, "verifyWithNafathDialog": {"title": "We’ve got your account’s security and\nprivacy in mind!", "nationalId": "Enter your National or Resident ID", "description": "We need your National or Resident ID to verify your\naccount through Nafath.", "nationalIdErrorMessage": "Nafath id is invalid"}, "authenticationPendingTitle": "Account authentication pending", "languagePreferencesDetails": {"cta": "edit", "title": "language preferences", "preferredLanguage": "Preferred language"}, "authenticationPendingSubtitle": "Authenticate your account to unlock property purchases, check eligibility, and enjoy a seamless ROSHN experience.", "editNotificationPreferencesDetails": {"title": "edit notification preferences", "smsNotifications": "SMS notifications", "pushNotifications": "Push notifications", "emailNotifications": "Email notifications", "whatsAppNotifications": "WhatsApp notifications"}}, "homepage": {"title": "A home designed for you", "explore": "explore", "bookViewing": "BOOK APPOINTMENT", "description": "Discover and Buy Your Dream Home in Thriving Communities - full on-line experience Explore Roshn's diverse portfolio of villas, including Contemporary Villa, Modern Villa, ranging from   1.5 million to  3 million. Immerse yourself in a lifestyle of convenience and connectivity at our family-friendly communities, featuring a comprehensive range of amenities, including mosques, schools, retail centers, parks, dining, entertainment, health centers, sports grounds, and kindergartens. Embrace the vibrant energy of Jeddah, Riyadh, and Sedra Al Alarous, all strategically located for easy access to major landmarks and main roads. Roshn: Your gateway to a fulfilling and enriching life", "registerInterest": "Register your interest"}, "notfound": {"pageNotFound": "Page not found\nPlease return to the homepage", "returnToHome": "Return to the Homepage"}, "communities": {"city": {"jeddah": "Jeddah", "makkah": "<PERSON><PERSON><PERSON>", "riyadh": "Riyadh", "dhahran": "<PERSON><PERSON><PERSON><PERSON>"}, "brochure": "Brochure", "homePage": {"sedra": "A new way of living", "warefa": "WELCOMING YOU TO A BEAUTIFUL NEW COMMUNITY", "alarous": "**History** of Jeddah, **Future** of Jeddah"}, "description": "ALAROUS community exudes sophistication with a meticulously planned layout. Essential amenities like hospitals, medical centres, schools, mosques, and shopping outlets complement distinctive secondary facilities, promising a harmonious and fulfilling existence for all residents.", "propertyType": {"villa": "Villas", "duplex": "Duplexes", "townhouse": "Townhouses", "premiumVilla": "Premium Villas"}, "communityName": {"sedra": "SEDRA", "warefa": "WAREFA", "alarous": "ALAROUS", "aldanah": "ALDANAH", "almanar": "ALMANAR"}, "propertyListing": "Property Listing in the Community Page", "viewingAmenities": "Viewing Amenities", "communityPageFooter": "Community <PERSON>", "propertySectionTitle": "Explore our homes", "displayCommunityBrief": "Display Community Brief and Numbers", "displayCommunityMasterplan": "Display Community Masterplan"}, "maintenance": {"title": "The website is under maintenance!", "description": "Apologies for the inconvenience, We’re currently performing\nscheduled maintenance to improve your experience.", "subDescription": "We’ll be back online shortly!"}, "appointments": {"myAppointments": "My Appointments"}, "salesAdvisor": {"qr": {"scan": "Scan the QR code or open the link to easily register your interest.", "share": "SHARE QR CODE", "connect": "We look forward to connecting with you!", "download": "DOWNLOAD QR CODE", "referral": "REFERRAL CODE"}, "signIn": {"logout": "You are logged out", "welcome": "Welcome To The Sales Advisor App!", "cmsError": "Something went wrong. Please try refreshing the page. If the problem persists, contact support. (Error Code: #CMS-ERR-02)", "continue": "Continue", "loggingIn": "We’re logging you in", "inputLabel": "Please login by entering your mobile phone number.", "tryAgainBtn": "Try to login again", "incorrectCode": "Invalid OTP. Please check the code and try again.", "editPhoneNumber": "Edit Phone Number", "unauthorizedAccess": "You’re not connected to ROSHN network. Please connect to ROSHN network or VPN and try again. For help, contact IT support.", "incorrectPhoneNumber": "Enter a 9-digit Saudi phone number starting with 5."}, "options": {"no": "No", "c10": "C10", "c20": "C20", "dp1": "DP1", "dp2": "DP2", "dp4": "DP4", "hot": "Hot", "th1": "TH1", "th3": "TH3", "vl1": "VL1", "vl2": "VL2", "vl3": "VL3", "yes": "Yes", "call": "Call", "cash": "Cash", "cool": "Cool", "warm": "Warm", "other": "Others", "price": "Price Issue", "sedra": "SEDRA", "villa": "Villa", "aldana": "ALDANAH", "duplex": "Duplex", "warefa": "WAREFA", "alArous": "ALAROUS", "almanar": "ALMANAR", "bedroom": "Bedroom", "followUp": "Follow Up", "sellUnit": "Brokerage - Home Owner (Resell)", "testLead": "Test Lead", "apartment": "Apartment", "archetype": "Archetype", "leaseUnit": "Brokerage - Home Owner (Lease)", "qualified": "Qualified", "townhouse": "Townhouse", "wrongTime": "Not Interested at The Current Stage", "annualFees": "Annual Fees", "noSuitUnit": "No Suitable Unit", "selfFunded": "Cash", "homeFinance": "Home Finance", "unqualified": "Unqualified", "buyReadyUnit": "Brokerage - Buyer", "deliveryDate": "Delivery Date", "decidedNotbuy": "Already Purchased a Unit", "duplicateLead": "Duplicate Lead", "productDesign": "Unpleasant Design", "rentReadyUnit": "Brokerage - Tenant", "roshnEmployee": "ROSHN Employee", "notContactable": "Can’t Be Contacted", "decidedBuybuilt": "Decided to Buy Ready Built Unit", "finalizePayment": "Finalize Payment", "loanNotpossible": "Loan Not Possible", "projectLocation": "Project Location", "roshnReputation": "ROSHN Reputation", "scheduleMeeting": "Schedule Meeting", "customerNotSaudi": "Customer not Saudi and not Premium Resident", "propertyPurchase": "Property Purchase", "serviceExperience": "Unpleasant Experience", "updateInformation": "Update Information", "notPurchaseEnquiry": "Non-Property Purchase Interest", "assignToHomeFinance": "Assign to Home Finance", "sendCancellationForm": "Send Cancellation Form", "noShowSalesCenterVisit": "No Show – Sales Center Visit", "interestedInAnotherCity": "Interested in Another City/Project", "theirFdrAlreadyPurchased": "Their FDR already purchased in ROSHN", "notInterestedAndWantsNoFu": "Not Interested in ROSHN", "interestedInAnotherProject": "Interested in Another Project"}, "holdUnit": {"price": "Price", "filter": {"all": "All", "expired": "Expired", "holdStatus": "Hold Status", "inProgress": "In Progress", "srnCreated": "SNR Created"}, "project": "Project", "community": "Community", "leadNumber": "Lead Number", "nafathInfo": "Nafath verification is required to hold a unit.", "selectLead": "Select lead number", "unitNumber": "Unit Number", "phoneNumber": "Phone Number", "customerName": "Customer Name", "errorMessage": "Hold unit unsuccessful", "selectProject": "Select Project", "selectCustomer": "Search by customer name or mobile number", "errorMessageCode": {"01.00.204": "User has resident ID. Cannot continue reservation on the digital journey.", "01.00.205": "Unit is currently reserved. Please try selecting another unit.", "01.00.878": "Premium residency holders cannot reserve units in this community. Please check customer details.", "01.00.901": "Unit is unavailable for reservation. Please check its status.", "10.00.323": "Mismatch in Contact Registry. Please contact the system owner.", "10.00.324": "Preferred unit type missing from the lead. Update the customer’s lead with a preferred unit type.", "10.00.330": "National/Resident ID not found in the system. Please make sure it’s added.", "10.00.331": "Customer is under 18 years old in Hijri. Please check the customer’s birthdate.", "10.00.332": "Missing date of birth. Please add the customer’s date of birth.", "10.00.335": "The customer’s Hijri date of birth is not recorded. Please update it in the contact details or request the customer to re-verify <PERSON><PERSON><PERSON>.", "10.01.302": "Authentication service unavailable. Please retry or contact the system owner."}, "salesRegisterStatus": {"SOLD": "SOLD", "DRAFT": "DRAFT", "RESERVED": "RESERVED", "SA_EXECUTED": "SA_EXECUTED", "CONTRACT_CANCELLED": "CONTRACT_CANCELLED", "RESERVATION_EXTENDED": "RESERVATION_EXTENDED", "RESERVATION_CANCELLED": "RESERVATION_CANCELLED", "SA_APPROVAL_IN_PROGRESS": "SA_APPROVAL_IN_PROGRESS", "RES_APPROVAL_IN_PROGRESS": "RES_APPROVAL_IN_PROGRESS", "RES_EXTENSION_IN_PROGRESS": "RES_EXTENSION_IN_PROGRESS", "SOLD_APPROVAL_IN_PROGRESS": "SOLD_APPROVAL_IN_PROGRESS", "CON_CANCELLATION_IN_PROGRESS": "CON_CANCELLATION_IN_PROGRESS", "RES_CANCELLATION_IN_PROGRESS": "RES_CANCELLATION_IN_PROGRESS"}, "unitNumberPlaceholder": "Enter unit number"}, "dashboard": {"name": "Welcome, <PERSON>", "title": "Welcome, {{salesAdvisorName}}", "masterplan": {"title": "Communities", "openMap": "open map", "viewArchetypes": "View list of PROPERTY TYPOLOGY"}, "searchUnits": {"item": {"unit": "unit number", "project": "project", "community": "community", "leadNumber": "lead number"}, "error": "Wrong Unit Number", "title": "search unit", "button": "go to unit page", "leadNumber": "Lead Number", "placeholder": {"project": "Select Project", "community": "Select Community", "leadNumber": "Select Lead Number"}, "unitPlaceholder": "Enter Unit Number"}, "unitsOnHold": "Hold Status", "followUpList": "My Follow Up List", "newCustomers": {"title": "Assigned Customers", "subtitle": "Below are the new dealings with your customers", "viewFullList": "View full Customer list"}, "insightsSummary": {"sar": "", "date": "Select date", "title": "Dashboard", "options": {"all": "All", "custom": "Custom date range", "last7Days": "Last 7 days", "thisMonth": "This month"}, "unitsSold": "Units Sold", "selectEndDate": "Select end date", "optionDisplays": {"custom": "Custom range"}, "unitsSoldValue": "{{ value }} units", "numReservations": "Number of Reservations", "selectStartDate": "Select start date", "assignedCustomers": "Assigned Customers", "reservationsValue": "{{ value }} leads", "amountReservations": "Total Reservation Value", "assignedCustomersValue": "{{ value }}", "amountReservationsValue": "{{ value }}"}, "quickNavigations": "Quick actions"}, "customerList": {"lead": "{{ number }}", "nafath": "<PERSON><PERSON><PERSON>", "search": "Search", "status": "Status", "clients": "{{ advisor }}'s Customers", "filters": {"all": "All", "hot": "Hot", "cash": "Cash", "cool": "Cool", "date": "Date", "sold": "Sold", "warm": "Warm", "draft": "Draft", "today": "Today", "booked": "Booked", "executed": "Executed", "leadRank": "Lead Rank", "thisWeek": "This Week", "thisYear": "This Year", "leadsOnly": "Leads Only", "thisMonth": "This Month", "allFilters": "All filters", "homeFinance": "Home Finance", "past3Months": "Past 3 Months", "past6Months": "Past 6 Months", "past9Months": "Past 9 Months", "customerType": "Customer Type", "customersOnly": "Customer Only", "paymentMethod": "Payment Method", "propertyStatus": "Status", "contractCanceled": "Contract canceled", "reservationCancelled": "Reservation canceled", "contractCancellationInProgress": "Contract cancellation in progress", "reservationCancellationInProgress": "Reservation cancellation in progress"}, "refresh": "Refresh List", "converted": "Converted", "qualified": "Qualified", "leadStatus": "Lead Status", "registryId": "Registry ID", "unqualified": "Unqualified", "propertyInfo": "Property information {{ number }}", "visitProfile": "Visit Profile"}, "followUpList": {"apply": "Apply Filter", "reset": "Reset Filters", "filter": {"C10": "C10", "C20": "C20", "DP1": "DP1", "DP2": "DP2", "DP4": "DP4", "TH1": "TH1", "TH3": "TH3", "VL1": "VL1", "VL2": "VL2", "VL3": "VL3", "all": "All", "hot": "Hot", "call": "Call", "cash": "Cash", "cool": "Cool", "done": "Done", "hold": "Unit Number", "warm": "Warm", "DRAFT": "Draft", "buyer": "Brokerage - Buyer", "lease": "Brokerage - Home Owner (Lease)", "notes": "Notes", "sedra": "<PERSON><PERSON>", "timer": "Time of Hold", "villa": "Villa", "budget": "Budget", "duplex": "Duplex", "expire": "Expired", "jeddah": "Jeddah", "marafi": "<PERSON><PERSON>", "resell": "Home Owner (Resell)", "riyadh": "Riyadh", "tenant": "Brokerage - Tenant", "warefa": "Warefa", "EXPIRED": "Expired", "alarous": "Alarous", "aldanah": "Aldanah", "almanar": "<PERSON>nar", "retired": "Retired", "waiting": "Waiting", "RESERVED": "Reserved", "al_arous": "Alarous", "followUp": "Follow Up", "followup": "Follow Up", "holdInfo": "Hold Status", "leadRank": "Lead Rank", "purchase": "Property Purchase", "unitType": "Unit Type", "CANCELLED": "Cancelled", "USABILITY": "Usability", "converted": "Converted", "leadStage": "Lead Stage", "qualified": "Qualified", "readiness": "Readiness", "townhouse": "Townhouse", "cancelForm": "Send Cancellation Form", "holdStatus": "Hold Status", "leadNumber": "Lead Number", "leadStatus": "Lead Status", "nextAction": "Next Actions", "writeEmail": "Write Email", "IN_PROGRESS": "In Progress", "SRN_CREATED": "SRN Created", "homeFinance": "Home Finance", "phoneNumber": "Phone Number", "unqualified": "Unqualified", "customerName": "Customer Name", "followUpDate": "Follow Up Date", "followUpList": "Follow Up List", "followUpTime": "Follow Up Time", "leadCategory": "Lead Category", "unitHoldDate": "Unit Hold Date and Time", "notInterested": "Not Interested", "paymentMethod": "Payment Method", "salesRegister": "Sales Register", "selectBedrooms": "Select bedroom(s)", "finalisePayment": "Finalize Payment", "scheduleMeeting": "Schedule Meeting", "selectBathrooms": "Select bathroom(s)", "unitPreferences": "Typology Preference", "reservationStatus": "Reservation Status", "updateInformation": "Update Information", "paymentPreferences": "Payment Preferences", "selectPropertyType": "Select a Property Type", "assignToHomeFinance": "Assign to Home Finance", "followUpDateAndTime": "Follow Up Date & Time", "salesRegisterStatus": "Sales Register Status", "communitiesOfInterest": "Communities of Interest", "interestedCommunities": "Interested Communities"}}, "notification": {"all": "All", "sms": "SMS notifications", "push": "Push notifications", "wait": "Please wait", "email": "Email notifications", "goBack": "Go Back", "unRead": "Unread", "allRead": "All the notifications are read", "confirm": "Confirm", "loading": "Loading Notifications", "refresh": "Refresh notifications", "viewAll": "View all notifications", "caughtUp": "You are all caught up", "markRead": "<PERSON> as read", "tryAgain": "Please try again!", "readError": "Failed to mark all as read, please try again", "markUnRead": "<PERSON> as unread", "markAllRead": "<PERSON> as read", "unavailable": "Notifications Unavailable", "unableToLoad": "We’re unable to load your notifications at the moment. Please try again later.", "notifications": "Notifications", "backToDashboard": "Back To Dashboard", "backToNotifications": "Go Back to Notifications", "notificationPreferences": "Notifications Preferences"}, "followUpTable": {"budget": "Budget", "leadRank": "Lead Rank", "unitType": "Unit Type", "leadNumber": "Lead Number", "nextAction": "Next Action", "phoneNumber": "Phone Number", "customerName": "Customer Name", "followUpDate": "Follow Up Date & Time", "followUpTime": "Follow Up Date & Time", "paymentMethod": "Payment Method", "communitiesOfInterest": "Community of Interest"}, "propertyGroup": {"title": "Community Property list", "breadcrumb": "PROPERTY LIST", "backToDashboard": "Back To Dashboard"}, "customerProfile": {"all": "All", "bank": "Financial Bank", "call": "Call", "copy": "Copy", "home": "Home", "lead": "Lead {{ number }}", "mail": "Email", "rank": "Lead Rank", "wait": "Please wait", "notes": "Notes", "owner": "Owner", "budget": "Budget", "addNote": "Add Note", "history": "History", "manager": "Manager", "payment": "Form of Payment", "retired": "Retired", "timeOut": "System timed out", "discount": "Discount", "leadRank": "Lead Rank", "property": "Properties", "someTime": "Please try again after some time!", "tryAgain": "Try again", "whatsapp": "Whatsapp", "community": "Communities of Intrest", "converted": "Converted", "documents": {"title": "Documents", "salesOffer": "Sales Offer", "reservationReceipt": "Reservation Receipt", "unitReservationForm": "Unit Reservation Form", "salesPurchaseAgreement": "Sales & purchase agreement"}, "leadStage": "Lead Stage", "maxAmount": "Maximum Amount", "minAmount": "Minimum Amount", "noHistory": "No Audits found", "qualified": "Qualified", "useSakani": "Use Sakani", "backToHome": "Back To Home", "favourites": "Favourites", "financeRep": "Finance Rep", "leadStatus": "Lead Status", "nextAction": "Next Actions", "noFavorite": "No favorite available", "noProperty": "Customer doesn't have any properties", "priceError": "No price associated with unit", "retireLead": "Retire Lead", "selectDept": "Select Department", "teamMember": "Team Member", "enterReason": "Retirement Comments", "holdSuccess": "Unit Successfully on hold", "leadActions": "Lead Actions", "leadDetails": "Lead Details", "noUnitFound": "No unit found with this unit number", "salaryRange": "Salary Range", "salesMethod": "Sales Method", "unqualified": "Unqualified", "creationDate": "Creation Date", "customerList": "Customer List", "customerName": "Customer Name", "errorMessage": "Lead update has failed, please try again.", "favoriteList": "List of all the customer’s favorite properties", "fetchingNote": "Fetching notes from the Lead...", "financeTypes": {"cash": "Cash (Self-Funded)", "selfFunded": "Cash (Self-Funded)", "SELF_FUNDED": "Cash (Self-Funded)", "homeFinance": "Home Finance Advisor", "HOME_FINANCE": "Home Finance"}, "followUpList": "Add to follow-up list", "leadCategory": "Lead Category", "noOfBedrooms": "No. of Bedrooms", "notAvailable": "This unit is not available", "propertyList": "List of all the customer’s properties", "receptionist": "Receptionist", "retiredError": "This lead cannot be edited as it has been retired.", "editLeadError": "This lead cannot be edited as it is currently assigned to {{ownerResourceName}}.", "favouriteList": "Favourite List", "financeMethod": "Financial Type", "formOfPayment": "Form of Payment", "requiredField": "{{<PERSON><PERSON><PERSON><PERSON>}} is a required field", "salesAdvisors": "Sales Advisor", "updatedPhrase": "updated for the following on {{ updatedAt }}", "bankableStatus": "Bankable status", "cityOfInterest": "City of Interest", "convertedError": "This lead cannot be edited as it has been converted.", "financeDetails": "Finance Details", "holdInProgress": "Unit hold is in progress. We’ll notify you once the hold is successful.", "mohBeneficiary": "MOH Beneficiary", "ownershipError": "Ownership update failed", "successMessage": "Lead details has been updated successfully", "suitableAmount": "Suitable Amount", "unitPreference": "Unit Typology Preference", "addFollowUpList": "Add to follow-up list", "confirmTransfer": "Confirm Ownership Transfer", "customerDetails": "Customer Details", "enterYourReason": "Enter Your Reason", "financeArranged": "Finance Arranged", "financeRepEmail": "Finance Rep Email", "loadingFavorite": "Loading favourites", "loadingProperty": "Loading properties", "financeRepMobile": "Finance Rep Mobile", "maxFinanceAmount": "Maximum Finance Amount", "maxFinancePeriod": "Maximum Finance Period (Month)", "ownershipSuccess": "Ownership transferred successfully", "retirementReason": "Retirement Reason", "selectPreference": "Select Preference", "selectTeamMember": "Select a team member to transfer ownership", "statusPriceError": "The unit is not available or there is a problem with the pricing", "furnishedProperty": "Furnished Property", "maxFinInstallment": "Maximum Finance Instalment", "noteCreationError": "Failed to create note", "recommendProperty": "Recommend A Property", "retirementReasons": "Retirement Reasons", "sakaniBeneficiary": "<PERSON><PERSON><PERSON> Beneficiary", "transferOwnership": "Transfer Ownership", "backToCustomerList": "Back To Customer List", "customerProperties": "Customer Properties", "expectedMoveInDate": "Expected Move In Date", "ownershipChangedTo": "Ownership would be changed to {{owner}}", "propertyPreference": "Property Preference", "typologyPreference": "Unit Typology Preference", "communityOfInterest": "Community of Interest ", "homeFinanceProvider": "Home Finance Provider", "propertyPreferences": "Property Preferences", "salesRegisterNumber": "Sales Register Number", "salesRegisterStatus": "Sales Register Status", "unitRecommendations": {"maid": "Maid’s room included", "error": "System timed out", "driver": "Driver’s room included", "emptyTypo": "No match found", "maidDriver": "Maid’s and driver’s room included", "unitLoading": "Loading recommendations", "requirements": "Here are the matches that best suit the customer's requirements", "compatibility": "Indicates the compatibility rate between the customer and the property.", "unitRefetchTypo": "Try again", "unitRecommendation": "Unit Recommendations"}, "financialEligibility": "Finance Eligibility", "homeFinanceClearance": "Home Finance Clearence", "communitiesOfInterest": "Communities of Interest", "interestedCommunities": "Interested Communities", "registryCopiedMessage": "Registry ID is copied to the clipboard", "retirementReasonsList": {"others": "Others", "testLead": "Test Lead", "unreached": "Unreached", "annualFees": "Annual Fees", "priceIssue": "Price Issue", "deliveryDate": "Delivery Date", "duplicateLead": "Duplicate Lead", "roshnEmployee": "ROSHN Employee", "noSuitableUnit": "No Suitable Unit", "personalReason": "Personal Reason", "cantBeContacted": "Can’t Be Contacted", "loanNotPossible": "Loan Not Possible", "projectLocation": "Project Location", "roshnReputation": "ROSHN Reputation", "unpleasantDesign": "Unpleasant Design", "noPurchaseInterest": "No Purchase Interest", "notInterestedInRoshn": "Not Interested in ROSHN", "unpleasantExperience": "Unpleasant Experience", "alreadyPurchasedAUnit": "Already Purchased a Unit", "noShowSalesCenterVisit": "No Show – Sales Center Visit", "decidedToBuyReadyBuiltUnit": "Decided to Buy Ready Built Unit", "interestedInAnotherProject": "Interested in Another Project", "nonPropertyPurchaseInterest": "Non-Property Purchase Interest", "interestedInAnotherCityProject": "Interested in Another City/Project", "notInterestedAtTheCurrentStage": "Not Interested at The Current Stage", "theirFdrAlreadyPurchasedInRoshn": "Their FDR already purchased in ROSHN", "customerNotSaudiAndNotPremiumResident": "Customer not Saudi and not Premium Resident"}, "suitableFinancePeriod": "Suitable Finance  Period (Month)", "suitableFinanceInstallment": "Suitable Finance Instalment"}, "fetchStateManager": {"favorite": {"emptyTitle": "No favorite available", "errorTitle": "System timed out", "loadingTitle": "Loading favourites", "errorButtonLabel": "Try again", "errorDescription": "Please try again!", "loadingDescription": "Please wait"}, "notification": {"emptyTitle": "You are all caught up", "errorTitle": "Notifications Unavailable", "loadingTitle": "Loading Notifications", "emptyDescription": "All the notifications are read", "errorButtonLabel": "Refresh notifications", "errorDescription": "We’re unable to load your notifications at the moment. Please try again later.", "loadingDescription": "Please wait"}, "unitRecommendation": {"emptyTitle": "No match found", "errorTitle": "System timed out", "loadingTitle": "Loading recommendations", "errorButtonLabel": "Try again", "errorDescription": "Please try again!", "loadingDescription": "Please wait"}}, "errorMessageFromCode": {"10.00.302": "Invalid lookup code passed to backend", "10.00.307": "CONVERTED or RETIRED lead cannot be updated", "10.00.309": "CONVERTED or RETIRED lead cannot be added to follow-up list"}}, "communitiesV2": {"sedra": {"amenitiesSection": {"title": "SEDRA AMENITIES", "subTitle": "Fully integrated and recreational amenities that encourage a healthy and balanced lifestyle."}, "aboutProjectSection": {"title": "About the project", "ctaLabel": "START RESERVING ONLINE", "description": "Located in northern Riyadh, SEDRA comprises beautifully designed homes complemented by mosques, local shops, cafes and restaurants, parks, schools and neighborhood entertainment spaces. in addition to its many other sports and fitness facilities.", "communityInfo": [{"icon": "https://alb-home.roshn.sa/buildings_3802fe0a27/buildings_3802fe0a27.svg", "label": "NO. OF UNITS", "value": "18K"}, {"icon": "https://alb-home.roshn.sa/money_bill_1_1e804738d9/money_bill_1_1e804738d9.svg", "label": "PRICE STARTS FROM", "value": "123,000 SAR"}, {"icon": "https://alb-home.roshn.sa/buildings_3802fe0a27/buildings_3802fe0a27.svg", "label": "UNIT PROTOTYPES", "value": "Villa, Duplex, Townhouse"}, {"icon": "https://alb-home.roshn.sa/park_e215a3b9ed/park_e215a3b9ed.svg", "label": "AMENITIES", "value": "+400"}]}, "exploreLocationsSection": {"title": "Explore our strategic location", "bgImage": "https://alb-home.roshn.sa/SEDRA_84c5383d84/SEDRA_84c5383d84.svg", "textColor": "#11100F", "locationsList": ["King Abdullah Financial City (KAFD) | 20 mins", "King Khalid International Airport | 15 mins", "Riyadh EXPO 2030 | 10 mins", "Princess <PERSON><PERSON>h University | 8 mins", "ROSHN Front | 5 mins"]}}, "warefa": {"amenitiesSection": {"title": "WAREFA AMENITIES", "subTitle": "Fully integrated and recreational amenities that encourage a healthy and balanced lifestyle."}, "aboutProjectSection": {"title": "About the project", "ctaLabel": "START RESERVING ONLINE", "description": "Presenting 'WAREFA,' a new community in the up-and-coming East of Riyadh, where residents enjoy a serene environment that enriches their lives with an integrated infrastructure and vibrant spaces meticulously maintained. All this is just steps away from essential amenities, allowing you to experience a new way of living.", "communityInfo": [{"icon": "https://alb-home.roshn.sa/buildings_3802fe0a27/buildings_3802fe0a27.svg", "label": "NO. OF UNITS", "value": "2,300"}, {"icon": "https://alb-home.roshn.sa/money_bill_1_1e804738d9/money_bill_1_1e804738d9.svg", "label": "PRICE STARTS FROM", "value": "123,000 SAR"}, {"icon": "https://alb-home.roshn.sa/buildings_3802fe0a27/buildings_3802fe0a27.svg", "label": "UNIT PROTOTYPES", "value": "Villa, Duplex, Townhouse"}, {"icon": "https://alb-home.roshn.sa/park_e215a3b9ed/park_e215a3b9ed.svg", "label": "AMENITIES", "value": "+400"}]}, "exploreLocationsSection": {"title": "Explore our strategic location", "bgImage": "https://alb-home.roshn.sa/SEDRA_84c5383d84/SEDRA_84c5383d84.svg", "textColor": "#11100F", "locationsList": ["King Fahd Stadium | 14 mins", "National Guard Hospital | 15 mins", "King Fahd Security College | 16 mins", "King Salman Park | 36 mins"]}}, "alarous": {"amenitiesSection": {"title": "ALAROUS AMENITIES", "subTitle": "Fully integrated and recreational amenities that encourage a healthy and balanced lifestyle."}, "aboutProjectSection": {"title": "About the project", "ctaLabel": "START RESERVING ONLINE", "description": "ALAROUS will be a refined, thoughtfully designed community, featuring a meticulously planned layout. With essential amenities such as medical centers, schools, mosques, shops, cafes, restaurants, and event spaces all within close proximity, ALAROUS will offer a people-centric, pedestrian-friendly environment, elevating the quality of life for all residents", "communityInfo": [{"icon": "https://alb-home.roshn.sa/buildings_3802fe0a27/buildings_3802fe0a27.svg", "label": "NO. OF UNITS", "value": "18k"}, {"icon": "https://alb-home.roshn.sa/money_bill_1_1e804738d9/money_bill_1_1e804738d9.svg", "label": "PRICE STARTS FROM", "value": "123,000 SAR"}, {"icon": "https://alb-home.roshn.sa/buildings_3802fe0a27/buildings_3802fe0a27.svg", "label": "UNIT PROTOTYPES", "value": "Villa, Duplex, Townhouse"}, {"icon": "https://alb-home.roshn.sa/park_e215a3b9ed/park_e215a3b9ed.svg", "label": "AMENITIES", "value": "300"}]}, "exploreLocationsSection": {"title": "Explore our strategic location", "bgImage": "https://alb-home.roshn.sa/SEDRA_84c5383d84/SEDRA_84c5383d84.svg", "textColor": "#fff", "locationsList": ["New King Faisal specialist hospital | 4 mins", "King <PERSON> Sport City | 7 mins", "King Abdulaziz International Airport | 8 - 17 mins", "The Beautiful Beaches of The Red Sea | 16 mins"]}}}, "propertyGroup": {"affordabilityCheckSuggestion": "Like our properties? Let’s do an affordability check!"}, "propertySection": {"done": "Done", "price": "Price available after prototype release", "title": "Explore available {{property}} prototypes", "subTitle": "{{communityName}} offers a variety of {{property}} prototypes consisting of four or five bedrooms with a variety of spaces."}, "cancelReservation": {"confirmationModal": {"title": "Cancel Reservation", "primaryCta": "START CANCELLATION", "description": "Are you sure you want to cancel your reservation?\nThe unit will no longer be reserved for you if you proceed", "secondaryCta": "discard"}, "somethingWentWrong": {"title": "Oops! Something went wrong", "primaryCta": "Try again", "description": "We couldn’t process your cancellation request at the moment.\nPlease try again later or contact support if the issue persists.", "secondaryCta": "Contact us"}, "acknowledgmentModal": {"cta": "DONE", "title": "Cancellation acknowledgment", "description": "We’ve received your cancellation request. It’s currently\n under review, and we’ll notify you via SMS & E-mail once\n it’s processed."}, "cancellationCallout": {"cta": "920 022 288", "title": "Cancellation request", "description": "Your cancellation request is under processing. in case you change your mind you can reach out to ROSHN Customer Care Centre"}, "requestCancellation": "Request cancellation", "cancellationDocument": {"esign": "E-SIGN DOCUMENT", "title": "Review Unit Cancellation & Refund Request", "consent": "I consent to use of my personal information for the digital signature generation with emdha Trust Service Provider.", "openPdf": "OPEN IN PDF VIEWER", "confirmOtp": "Confirm your identity to e-sign", "loadingText": "Please wait while we complete the signing. Please Do not\nclose this page"}, "cancellationBankModal": {"title": "Cancellation Request", "toolTip": {"title": "Why IBAN?", "description": "Your IBAN is essential\n for processing your refund"}, "primaryCta": "CONTINUE CANCELLATION", "refundBank": {"question": "Select the bank where the refund will be transferred", "placeholder": "select"}, "uploadFile": {"caption": "JPG, PNG, HEIF, HEIC or PDF less than 5MB", "question": "Upload IBAN Certificate", "placeholder": "Browse or drop a file", "fileSizeError": "File exceeds the 5 MB size limit.", "invalidFileTypeError": "Only PDF, PNG or JPEG formats are allowed.", "invalidFileTypeAndFileSizeError": "File must be a PDF, PNG or JPEG and must not exceed 5 MB."}, "secondaryCta": "discard", "refundBankIban": {"question": "Enter your bank account IBAN", "placeholder": "SAXX XXXX XXXX XXXX XXXX XXXX", "errorMessage": "The IBAN you entered is invalid. Please try again.", "errorMessageWrongLength": "Your IBAN length is invalid."}}, "cancellationReasonModal": {"title": "Cancellation Request", "extraNote": {"question": "Would you like to provide more details?", "placeHolder": "Entre more details"}, "primaryCta": "CONTINUE CANCELLATION", "description": "Are you sure you want to cancel your reservation? The\nunit will no longer be reserved for you if you proceed.", "cancelReason": {"question": "Select a reason for cancellation", "placeholder": "Select"}, "secondaryCta": "discard", "cancelSubReason": {"question": "Select the most relevant option below", "placeholder": "Select Sub Reason"}}, "cancellationUnderProcessingTag": "Cancellation request under processing"}, "thankYouForRegistering": {"title": "Thank you for registering your interest", "served": "Kindly wait to be served.", "qmsTitle": "You have successfully checked in!", "tryAgain": "Try again", "wentWrong": "Something went wrong. Please try again!", "noCapacity": "Opps, no available capacity", "closeWindow": "You may close this window now. You will receive an sms when it is your turn to see the receptionist.", "description": "When it's available, we will reach out shortly to arrange an appointment for you to visit our sales centre.", "goToJourney": "ROSHn’s digital journey", "queuePosition": "Your queue position would be ", "digitalJourney": "Explore ROSHN’s Digital Journey while you wait", "failedToCreate": "<PERSON><PERSON>, failed to create appointment", "qmsDescription": "Your position in line to see the receptionist", "returnHomePage": "Return to the homepage", "checkInUnsuccessFull": "<PERSON><PERSON>, check-in unsuccessful!"}}, "common": {"no": "No", "all": "All", "any": "any", "sar": "", "sqm": "sqm", "yes": "Yes", "edit": "Edit", "hide": "<PERSON>de", "hold": "Confirm Hold", "home": "Home", "over": "Over", "email": "Email", "callUs": "call us", "cancel": "Cancel", "goBack": "Go Back", "select": "Select", "confirm": "confirm", "discard": "Discard", "premium": "Premium", "continue": "Continue", "holdUnit": "Hold Unit", "readMore": "Read More", "department": "Department", "phoneNumber": "Phone Number", "saveChanges": "Save Changes", "propertyType": {"villa": "villa", "duplex": "duplex", "apartment": "Apartment", "townhouse": "townhouse"}, "confirmChanges": "Confirm changes", "discardChanges": "Discard all the changes made", "somethingWrong": "Oops! Something went wrong. Please try again"}, "layout": {"menu": {"menu": "<PERSON><PERSON>", "news": "News", "login": "<PERSON><PERSON>", "logout": "Logout", "aboutUs": "About Us", "profile": {"CTA": {"reserveOnline": "To be able to reserve or buy a home online", "verifyToUnlock": "Verify to unlock unique features!", "verifyMyAccount": "Verify my account", "whyVerifyAccount": "Why do I need to verify my account now?", "checkAffordability": "To check affordability", "manageFinanceOnline": "To manage my home finance online"}, "edit": "Edit", "email": "Email address", "phone": "Phone number", "profile": "Profile", "fullName": "Full name", "lastName": "Last name", "verified": "Verified", "editEmail": "<PERSON> Email", "firstName": "First name", "unverified": "Unverified", "emailVerified": "<PERSON><PERSON>", "nafathVerified": "<PERSON><PERSON><PERSON>", "emailUnverified": "Email Unverified", "nafathUnverified": "Nafath Unverified"}, "contacts": {"faq": "FAQS", "email": "Email", "phone": "ROSHN Customer Care", "openMap": "Open on google map", "location": "ROSHN Real Estate Headquarters", "contactUs": "Contact us", "contactSubtitle": "Reach out to us if ou have any questions and an experienced specialist will be happy to assist you.", "saleCenterAddress": {"sedra": "SEDRA sales centre address", "warefa": "WAREFA sales centre address", "alarous": "ALAROUS sales centre address", "aldanah": "ALDANAH sales centre address", "alfulwa": "ALFULWA sales center address", "al-manar": "AL-MANAR sales centre address"}, "callCenterWorkingHours": {"title": "ROSHN Customer Care Center Working Hours", "workingHours": "09:00 am - 09:00 pm\nSaturday - Thursday"}, "salesCentreWorkingHours": {"title": "Sales centre working hours", "workingHours": "10.00 am - 07.00 pm\nSunday - Thursday"}}, "myAccount": {"dashboard": "Dashboard", "myAccount": "My Account", "favourites": "Favourites", "properties": "My Properties", "appointments": "My Appointments", "propertiesDetail": "Property"}, "allCommunity": "All Communities", "notificationPreferences": "Notifications Preferences"}, "footer": {"version": "Version", "followUs": "Follow us on Social Media", "copyright": "Copyright", "disclaimer": "Disclaimer", "stickyFooter": {"note": "*Online home purchase is currently in beta version", "accept": "Accept", "reject": "Reject", "useCookie": "Cookies page.", "cookiePage": "https://www.roshn.sa/en/privacy-policy", "disclaimer": "We use necessary cookies to make this website work. We may also use profiling cookies to provide you with a customised experience, only if you accept them. For more information, see our"}, "privacyPolicy": "Privacy Statement", "allRightsReserved": "2024 ROSHN GROUP, All rights reserved", "termsAndConditions": "Terms & Conditions"}, "header": {"tabs": {"home": "Home", "contactUs": "ROSHN Care", "myAccount": {"title": "Hi,", "logout": "Logout", "myAccount": "My Account", "favourites": "Favourites", "loggingOut": "logging out..", "description": "Welcome back! Manage your account and preferences here.", "myProperties": "My Properties", "myAppointments": "My Appointments"}, "communities": {"sedra": "SEDRA", "title": "Communities", "warefa": "WAREFA", "alarous": "ALAROUS", "aldanah": "ALDANAH", "almanar": "ALMANAR", "description": "Our residential communities are distinguished by their vibrant and fully integrated design, offering a diverse range of homes to suit all preferences and budgets—from elegant apartments to luxurious villas."}, "contactUsUrl": "https://www.roshn.sa/roshn-care"}, "kitchenBanner": {"hi": "Hi", "dream": "we are happy to inform you that you can choose the preferred colour of your kitchen at no cost. Selection of kitchen colour will only be available until 31st of December", "customize": "Customize now"}, "betaDisclaimer": "Online home purchase is currently in beta version. "}}, "features": {"edm": {"error": {"title": "Link expired", "message": "Looks like this link is no longer valid. Check 'My Appointments' for your active appointment details"}}, "cash": {"milestones": "Milestones", "noteCalculatorReservation": "Please note that RETT price is exclusive in the milestones. Within the handover period, you are responsible for paying RETT on the total value of your housing."}, "signUp": {"form1": {"checkIn": "Check-In", "welcome": "Welcome to ROSHN", "continue": "Continue", "greeting": "Please sign-up or log-in by entering your mobile phone number", "iAgreeTo": "I agree to the", "wentWrong": "Something went wrong. Please try again!", "placeholder": "Enter phone number", "lazyLoadLabel": "Loading more countries...", "mobileWelcome": "Welcome to ROSHN", "welcomeBanner": "Welcome to ROSHN", "completeCheckIn": "Complete check in", "incorrectNumber": "The mobile number is incorrect", "privacyStatement": "Privacy Statement", "termsAndConditions": "Terms & Conditions."}, "form2": {"in": " in ", "resend": "Resend", "confirmCode": "Confirm code", "description": "Please enter the 6-digit code sent to your mobile number <b><ltr>{{phoneNumber}}</ltr></b> via SMS", "errorLength": "OTP must be {{otpLength}} digits", "serverError": "SERVER_ERROR", "description3": "Please enter the 4-digit code  sent to your mobile number via SMS", "unknownError": "UNKNOWN_ERROR", "errorRequired": "OTP is required", "incorrectCode": "Oops! It looks like the code you entered isn't quite right"}, "form3": {"email": "Email", "welcome": "Welcome to ROSHN", "continue": "Continue", "lastName": "Last name", "firstName": "First name", "description": "Please enter your name and e-mail to create the account", "invalidEmail": "Oops! It looks like the email you entered isn't valid", "createAccount": "Register an account", "invalidLastName": "Please provide valid last name", "invalidFirstName": "Please provide valid first name"}, "skipNow": "verify account later", "leadForm": {"hi": "Hi", "any": "Any", "back": "Back", "cash": "Cash", "next": "Next", "over": "Over", "save": "Save", "learn": "We need to learn more about you, so the sales advisor can provide support tailored to your needs.", "steps": "Its as simple as 1,2 and 3!", "title": "Tell us a bit about yourself", "under": "Under", "submit": "Submit", "titles": {"first": "We’d like to know more about you", "third": "How many bedrooms do you need?", "second": "Which communities are you interested in?"}, "confirm": "Confirm", "example": "Example floorplan", "continue": "Continue", "headings": {"first": "Step 1 of 3: Tell us about yourself", "third": "Step 3 of 3: How many bedrooms do you need?", "second": "Step 2 of 3: Select communities of interest"}, "askBudget": "Budget range for new home?", "askBedroom": "How many bedrooms do you need?", "newCheckIn": "New Customer Check In", "askIsSakani": "Are you a Sakani beneficiary?", "communities": {"sedra": "SEDRA - Riyadh", "warefa": "WAREFA - Riyadh", "alarous": "ALAROUS - Jeddah", "aldanah": "ALDANAH - Dhahran", "almanar": "ALMANAR - <PERSON><PERSON><PERSON>"}, "description": {"first": "Up next: Select communities of interest", "third": "Up next: Tell us the number of rooms required", "second": "Up next: Tell us the number of rooms required"}, "homeFinance": "Home finance", "remindTitle": "Please fill out your personal info to log-in", "askCommunity": "Which communities are you interested in?", "remindButton": "Fill out my personal info", "titleWelcome": "We kindly ask you to share more information about yourself to get started.", "failureButton": "Try again", "successButton": "Continue Browsing", "askFinancePlan": "How do you plan to pay for your home?", "askSalaryRange": "Current salary range", "failureHeading": "Failed to submit your interest!", "failureMessage": "Something went wrong during your interest registration. Please try again", "successHeading": "Thank you for registering your interest!", "successMessage": "Your interest has been registered successfully! We're thrilled to assist you in finding your dream home.", "errRequireFields": "Please fill all required field", "remindDescription": "we noticed that you’ve created an account, but personal information is missing", "askBudgetPlaceholder": "Select budget range", "askBedroomPlaceholder": "Select the number of bedrooms", "registerInterestTitle": "Tell us a bit about you!", "askCommunityPlaceholder": "Select community", "registerInterestCaption": "All fields are mandatory to submit your request", "registerInterestSubtitle": "We kindly ask you to share more information about yourself to get started", "askSalaryRangePlaceholder": "Select salary range"}, "frErrMsgs": {"LOGIN_FAILURE": "Login Failed! Please try again", "USER_LOCKED_OUT": "Account is locked due to wrong password. Please try again in {{lockOutDuration}}.", "SESSION_HAS_TIMED_OUT": "Session Timeout! Your session has expired due to inactivity. Please log in again", "MAXIMUM_RETRY_EXCEEDED": "Maximum retries exceeded. Please login again.", "NO_CONFIGURATION_FOUND": "No configuration found", "MAXIMUM_RESEND_EXCEEDED": "Maximum resend for OTP exceeded. Please login again.", "UNKNOWN_REQUEST_FAILURE": "Unknown error! Please try again", "CAPTCHA_VERIFICATION_FAILED": "You have been blocked by reCAPTCHA. Please try again later.", "USER_LOCKED_OUT_PERMANENTLY": "Due to security reasons, your account has been permanently locked. If you believe this is a mistake or need further assistance, please contact us"}, "passwordForm": {"title": "We've got your account's security and\nprivacy in mind!", "defined": "Password must be defined", "message": "Before we proceed, we kindly ask you to enter your password", "enterPassword": "Enter password?", "forgotPassword": "Forgot password?", "incorrectPassword": "The password is incorrect"}, "resetPassword": {"title": "Reset password"}, "setUpPassword": {"title": "We've got your account's security and\nprivacy in mind!", "message": "Before we proceed, we kindly ask you to set up your password", "enterPassword": "Enter password", "mustBeDefined": "Password must be defined", "confirmPassword": "Confirm password", "passwordValidation": "Please input at least 8 characters, include at least one number, one uppercase letter, lowercase letter and one special character", "confirmPasswordValidation": "Passwords not matching", "passwordAlreadyConfigured": "Password is already configured!"}, "postResetPassword": {"later": "Later", "title": "Your new password has been saved!", "description": "Please verify your account again using Nafath, or access stays limited.", "suggestVerify": "Verify my account"}}, "account": {"changeEmail": {"title": "Change your email", "confirm": "CONFIRM", "required": "Required", "enterEmail": "Please enter your new email address", "serverError": "Oops! Something went wrong", "invalidEmail": "Oops! It looks like the email you entered isn't valid", "successMessage": "The email has been changed successfully", "emailPlaceHolder": "Enter your email"}, "suggestVerify": {"title": "Account verification required", "message": "Thank you for providing additional information. To ensure the security of your account, we kindly request that you verify it. You will be unable to reserve or buy a home until this step is completed."}, "accountBlocked": {"title": "Issue identified with your account", "address": "<PERSON> St, Al Khalidiyyah Jeddah, Saudi Arabia", "description": "To resolve the issue with your account, please contact us for further information and then refresh the page. Our team is ready to assist you promptly."}, "nationalIdForm": {"terms": "I acknowledge and approve that my details provided previously to the Developer in any Sales and Purchase Agreements will be updated based on the information received from Nafath. I further acknowledge and approve that all upcoming communications for all Sales and Purchase Agreements, such as but not limited to: invoices, announcements, issued by the Developer will be sent to most recent contact information received by the Customer.", "title": "We've got your account's security and privacy in mind!", "nationalId": "National or Resident ID", "description": "We need your National or Resident ID to verify your account through Nafath.", "errorInvalid": "The national or resident ID is incorrect", "errorRequired": "The national or resident ID is required", "verifyDescription": "Please enter the National or Resident ID you verified with.", "nationalIdIsIncorrect": "The national or resident ID is incorrect"}, "numberMatching": {"title": "Open the Nafath app to verify your account", "description": "Please open your Nafath app and select the code below to complete your ID verification"}, "updatePhoneNumber": {"title": "Change your phone number", "message": "Please enter your new phone number", "serverError": "Oops! something went wrong", "numberExisted": "Phone number already exists", "successMessage": "The phone number has been changed successfully! You can only edit your phone number once in a session, to edit your phone number again please logout and re-login.", "enterDifferentNumber": "The phone number you entered is already linked to another account. Please enter a different number"}, "nationalIdVerifyForm": {"terms": "I acknowledge and approve that my details provided previously to the Developer in any Sales and Purchase Agreements will be updated based on the information received from Nafath. I further acknowledge and approve that all upcoming communications for all Sales and Purchase Agreements, such as but not limited to: invoices, announcements, issued by the Developer will be sent to most recent contact information received by the Customer.", "title": "We've got your account's security and\nprivacy in mind!", "nationalId": "National or Resident ID", "description": "Please enter the National or Resident ID you verified with.", "errorInvalid": "The national or resident ID is incorrect", "errorRequired": "The national or resident ID is required", "verifyDescription": "Please enter the National or Resident ID you verified with.", "nationalIdIsIncorrect": "The national or resident ID is incorrect"}, "verifyForReservation": {"title": "Account verification required", "message": "In order to reserve a home, we kindly request that you verify your account. You will be unable to reserve or buy a home until this step is completed."}}, "bookings": {"scheduleError": {"back": "BACK"}, "noAppointments": "There is no scheduled appointment", "scheduleDialog": {"error": {"title": "Schedule a visit", "description": "We are very sorry that it seems this time slot is no longer available or due to some technical issues that we could not confirm your booking.\n\nPlease go back and try again, we thank you for your patience", "feedbackMessage": "Oops! something went wrong"}, "unavailable": {"title": "Sorry, there is no available appointment", "description": "We're sorry to say that we don't have any appointments available at the moment. But don't worry! You can reach out to us and we'll do our best to assist you.", "feedbackMessage": "Oops! something went wrong"}}, "appointmentCard": {"demo": {"saleCenterName": "Demo Sale Center", "saleCenterAddress": "Demo Sale Center Address"}, "content": {"phone": "Phone", "visit": "Visit", "cancel": "CANCEL", "checkIn": "Check In", "reschedule": "RESCHEDULE", "addToCalendar": "ADD TO CALENDAR", "pleaseReachOut": "Please reach out to us if you have any questions or need to reschedule.", "addToAppleWallet": "https://alb-home.roshn.sa/Add_to_Apple_Wallet_Narrow_Large_5a021c37c3/Add_to_Apple_Wallet_Narrow_Large_5a021c37c3.svg", "continueBrowsing": "CONTINUE BROWSING", "addToGoogleWallet": "https://alb-home.roshn.sa/Add_to_google_wallet_ff81bcc776/Add_to_google_wallet_ff81bcc776.svg", "appointmentCardTitle": "Sales Centre visit ", "confirmedAppointment": "Hello, we have confirmed your appointment at ", "appointmentNumberText": "Appointment no:", "appointmentCustomerCareText": "ROSHN Customer Care Centre:"}}, "bookAppointment": "Book a new appointment", "qmsReceptionist": {"done": "Done", "note": "Notes", "assign": "Attend", "remove": "Remove", "select": "Select", "status": "Status", "upload": "Upload", "walkIn": "Walk-In", "capacity": "The walk-in capacity has been reached at the moment. To ensure no inconvenience is caused to our visitors, please contact the Management at the earliest opportunity to increase the available capacity.", "noWalkIn": "No walk-in found", "showLess": "Show Less", "showMore": "Show More", "noVisitor": "No visitor found", "uploadCsv": "Upload file", "appointment": "Appointment", "browseFiles": "Browse files", "checkInTime": "Check-in Time", "goodEvening": "Good Evening", "goodMorning": "Good Morning", "phoneNumber": "Phone Number", "waitingTime": "Waiting time", "customerName": "Visitor Name", "salesAdvisor": "Sales Advisor", "userNotFound": "No user found", "visitorQueue": "Visitor queue", "waitingTable": "Assigned table", "goodAfternoon": "Good Afternoon", "noAppointment": "No appointment found", "totalCapacity": "Total walk-in capacity", "uploadNewFile": "Upload file", "uploadSuccess": "Your file has been successfully uploaded.", "appointmentTime": "Appointment Time", "notePlaceholder": "Enter Note (Optional)", "attendedCheckIns": "Attended Check Ins", "salesQueueAdvisor": "Sales advisor queue", "chooseASalesAdvisor": "<PERSON>ose a sales advisor", "withPriorAppointment": "Visitor with priority", "uploadCsvOfSalesAdvisors": "Please upload csv file for sales advisors"}, "scheduleSuccess": {"appointments": "Appointment", "pendingNotice": "Your appointment is currently awaiting confirmation. As soon as it is confirmed, you will receive a notification", "confirmCaption": "Due to the large amount of request, please make sure that you receive the confirmation before the visit.", "pendingAppointment": "You will receive a notification once your appointment is confirmed. If you have any questions, Contact", "confirmedAppointment": "Your appointment is confirmed! We look forward to seeing you at the sales center. If you have any questions, feel free to contact"}, "visitSaleCenter": {"whichCommunity": "Which community are you interested in to live?", "whichSaleCenter": "Which sales centre location is most convenient for you?", "selectSalesCenter": "Select sales center"}, "cancelAppointment": {"keep": "KEEP THE APPOINTMENT", "cancel": "YES, CANCEL THE APPOINTMENT", "whyCancel": "Why do you want to cancel it?", "description": "In case of any unexpected issues, we are here to assist you. Please contact us to reschedule your appointment or for any other inquiries.", "reasons_one": "{{count}} reason", "confirmation": "Are you sure you want to cancel your appointment?", "reasons_other": "{{count}} reasons", "selectReasons": "Please select reasons"}, "bookOneAppointment": {"sorry": "Sorry, you can only book one appointment at a time", "confirmed": "CONFIRMED", "rescheduleOrCancelYourAppointment": "If you need to reschedule or cancel your appointment, you can easily make changes here. Just a few clicks and you're all set!"}, "scheduleSelectSlot": {"confirm": "CONFIRM", "evening": "Evening", "morning": "Morning", "whatTime": "Available time slots:", "afternoon": "Afternoon", "disclaimer": "Please note that reservations in ALMANAR community are open to Saudi nationals only", "selectDate": "Select date", "whenToCall": "When would you like to have the call?", "whenToVisit": "When would you like to visit", "whereToVisit": "Where you would like to visit", "whichCommunity": "Which communities are you interested in to visit?", "selectCommunity": "Select community", "salesCentreVisit": "Sales Centre visit"}, "virtualAppointment": {"card": {"content": {"dateTime": "DATE & TIME", "location": "LOCATION", "addToCalendar": "ADD TO CALENDAR", "videoCallLink": "VIDEO CALL LINK", "videoCallLinkUrl": "Click here to join the call (at the scheduled time)", "adddTocalendarLink": "Click here to add to calendar", "videoCallDescription": "Check your email or this page for the call link 30 minutes before the start", "appointmentNumberText": "APPOINTMENT NUMBER"}, "virtualTitle": "Sales video call", "physicalTitle": "Sales Centre visit "}, "form": {"slots": {"label": "Available time slots:"}, "title": "Book appointment", "button": "Book Appointment", "options": {"virtual": "Video call", "physical": "Visit sales centre"}, "dateTime": {"label": "When would you like to have the call?"}, "community": {"label": "Which community are you interested in?", "placeHolder": "Select"}, "salesCenter": {"label": "Which sales centre advisor would you like to speak with?", "placeHolder": "Select"}, "noAppointment": {"desc": "Appointments are currently unavailable for this community, but our team is here to help! Get in touch, and we’ll do our best to assist you", "title": "No Appointments Available!"}, "appointmentType": {"label": "What’s your preferred way to speak to our sales advisor?"}, "rescheduleTitle": "Reschedule appointment"}, "homeBanner": {"overlay1": "Would you like to know more? Visit our sales centre.", "overlay2": "Can’t make it to a sales centre? Book a virtual appointment!"}, "toastMessage": {"slotNotAvailable": "Time slot unavailable. Please pick another."}, "confirmDescription": "Your appointment is confirmed! We look forward to meeting you online. If you have any questions, Feel free to contact"}, "scheduleUnavailable": {"contactUs": "CONTACT US"}}, "metadata": {"title": {"home": "ROSHN"}, "keywords": {"home": "properties, community, Saudi"}, "description": {"home": "Buy your home today in ROSHN’s integrated communities; SEDRA, ALAROUS, and WAREFA. Explore different types of units ranging from Villas, Duplexes, Townhouses, or apartments and complete your journey to own your dream home online."}}, "favourite": {"noFavouriteCTA": "Browse properties", "noFavouriteMessage": "You did not add any properties to your Favourites yet."}, "masterPlan": {"name": {"facadeType": {"sedra": {"traditional": "Traditional", "contemporary": "Contemporary"}, "warefa": {"traditional": "Traditional", "contemporary": "Contemporary"}, "alarous": {"modern": "Modern", "contemporary": "Contemporary"}, "aldanah": {"modern": "Modern", "traditional": "Traditional", "contemporary": "Contemporary"}, "almanar": {"modern": "Modern", "hijazzy": "<PERSON><PERSON><PERSON>", "contemporary": "Contemporary"}}, "propertyType": {"villa": "Villa", "duplex": "Duplex", "apartment": "Apartment", "townhouse": "Townhouse", "premiumVilla": "Premium Villa"}}, "cityView": {"health": "Healthcare", "retail": "Shops", "showAll": "show all", "distance": "distance", "amenities": "Nearby facilities", "education": "Schools", "landmarks": "Landmarks", "grandMosques": "Mosques", "nearbyFacilities": "Nearby facilities"}, "zoneView": {"ncc": "neighborhood community centers", "text": "Hello from Roshn,\nthis property might be interesting to you: ", "title": "ROSHN", "mosque": "Mosques", "police": "police", "retail": "Shops", "filters": "Filters", "showAll": "show all", "timerDes": "The Unit is on Hold for", "amenities": "Amenities", "education": "Schools", "landmarks": "Landmarks", "greenAreas": "green areas", "linkCopied": "Link copied to the clipboard", "listButton": "list", "nearbyFacilities": "Nearby facilities"}, "breadcrumb": {"city": {"home": "Home", "sedra": "Riyadh", "makkah": "<PERSON><PERSON><PERSON> ", "warefa": "Riyadh", "alarous": "Jeddah", "aldanah": "<PERSON><PERSON><PERSON><PERSON>", "almanar": "<PERSON><PERSON><PERSON>", "dhahran": "<PERSON><PERSON><PERSON><PERSON>"}, "zone": {"name": {"sedra": {"a": "Zone A", "b": "Zone B", "c": "Zone C", "d": "Zone D", "e": "Zone E", "f": "Zone F", "g": "Zone G", "h": "Zone H", "j": "Zone J", "k": "Zone K", "l": "Zone L", "m": "Zone M", "gc": "Zone GC"}, "warefa": {"a": "Zone A", "c": "Zone C", "d": "Zone D", "e": "Zone E", "f": "Zone F"}, "alarous": {"a": "Zone A", "b": "Zone B", "c": "Zone C"}, "aldanah": {"2": "Phase 2", "1c": "Phase 1"}, "almanar": {"1a": "Zone A", "1b": "Zone B"}}, "units": "{{units}} Available Units", "status": {"sold": "Sold out", "available": "Released", "unreleased": "Unreleased"}, "comingsoon": "Coming Soon", "allUnitReserved": "All Units Reserved"}, "legends": {"stores": "Stores", "travel": "Travel", "schools": "Schools"}, "project": {"sedra4": "SEDRA 4", "sedra_3": "SEDRA 3", "sedra_5": "SEDRA 5", "sedra_2a": "SEDRA 403", "sedra_4a": "SEDRA 4A", "warefa_1": "WAREFA 1", "aldanah_1": "ALDANAH", "almanar_1": "ALMANAR 1", "alarous_1a": "ALAROUS 1A"}, "community": {"sedra": "SEDRA", "warefa": "WAREFA", "alarous": "ALAROUS", "aldanah": "ALDANAH", "almanar": "ALMANAR"}}, "unitDetail": {"bedrooms": "bedrooms", "floorPlans": "floorplans", "unitDesign": "unit design", "unitNumber": "unit number", "builtUpArea": "built-up area", "shareButton": "share", "viewDetails": "View details", "notAvailable": "Not available", "loginToSeePrice": "login to see price", "startReservation": "start reservation"}, "unitFilter": {"any": "any", "type": "Unit type", "price": "Price", "reset": "Reset", "villa": "villa", "duplex": "duplex", "addRange": "Add range", "bedrooms": "Bedrooms", "resetAll": "Reset All", "typology": "Typology", "bathrooms": "Bathrooms", "townhouse": "townhouse", "applyFilter": "Apply Filters", "moreFilters": "More Filters", "showAvailable": "show available", "unitPreference": "Unit Preference", "nearbyFacilities": "Nearby Facilities"}, "houseFilter": {"all": "All", "any": "Any", "villa": "Villa", "duplex": "Duplex", "townhouse": "Townhouse"}, "communityView": {"mosque": "Mosques", "retail": "Shops", "message": "Choose a zone and use filters to find your unit", "showAll": "show all", "distance": "distance", "amenities": "Nearby facilities", "education": "Schools", "greenAreas": "green areas"}, "amenitiesFilter": {"Retail": "Shops", "health": "Healthcare", "showAll": "show all", "distance": "distance", "amenities": "Amenities", "education": "Schools", "greenArea": "green area", "landmarks": "landmarks", "grandMosque": "Mosques"}, "cityViewAmenities": {"logo": {"sedra": {"sedra2Title": "<title>SEDRA 403</title>", "sedra3Title": "<title>SEDRA 3</title>", "sedra4Title": "<title>SEDRA 4</title>", "sedra5Title": "<title>SEDRA 5</title>", "roshnFrontKM": "<roshnFrontKM>1.8 KM</roshnFrontKM>", "exploreSedra2": "<text>EXPLORE</text><n/><title>SEDRA 2</title>", "exploreSedra3": "<text>EXPLORE</text><n/><title>SEDRA 3</title>", "exploreSedra4": "<text>EXPLORE</text><n/><title>SEDRA 4</title>", "riyadhExpoText": "<riyadhExpoTxt>RIYADH EXPO 2030</riyadhExpoTxt>", "roshnFrontText": "<roshnFrontTxt>ROSHN Front</roshnFrontTxt>", "airportRoadText": "<airportRdTxt>Airport Road</airportRdTxt>", "dammamhRoadText": "<dammamRdTxt>Dammam Road</dammamRdTxt>", "khuraisRoadText": "<khuraisRdTxt>Khurais Road</khuraisRdTxt>", "riyadhExpoKMText": "<riyadhExpoKM>12 KM</riyadhExpoKM>", "thumamahRoadText": "<thumamahRdTxt>Thumama Road</thumamahRdTxt>", "warefaCommunityKM": "<warefaCommunityKM>22 KM</warefaCommunityKM>", "KingSalmanRoadText": "<kingSalmanRdTxt>King Salman Bin Abdul<PERSON>z Road</kingSalmanRdTxt>", "warefaCommunityText": "<warefaCommunityTxt>WAREFA Community</warefaCommunityTxt>", "northernRingRoadText": "<northernRingRdTxt>Northern Ring Road</northernRingRdTxt>", "princessNouraUniversityKM": "<princessNouraUnivKM>7.5 KM</princessNouraUnivKM>", "princessNouraUniversityText": "<princessNouraUnivTxt>Princess <PERSON>ura <PERSON></princessNouraUnivTxt>", "kingKhalidInternationalAirportText": "<kingKhalidAirportTxt>King Khalid International Airport</kingKhalidAirportTxt>", "kingKhalidInternationalAirportKMText": "<kingKhalidAirportKM>13 KM</kingKhalidAirportKM>"}, "aldanah": {"dahranExpoText": "<dahranExpoTxt>Dahran Expo</dahranExpoTxt>", "imamUnivKMText": "<imamUnivKM>10 KM</imamUnivKM>", "theAvenuesText": "<theAvenuesTxt>The Avenues</theAvenuesTxt>", "dahranExpoKMText": "<dahranExpoKM>14 KM</dahranExpoKM>", "jalaluddinStText": "<jalaludinStTxt><PERSON><PERSON><PERSON>ddin Al Sayuti st</jalaludinStTxt>", "kingFahdRoadText": "<kingFahdRdTxt>King Fahd Road</kingFahdRdTxt>", "kingSaudRoadText": "<kingSaudRdTxt>King Saud Road</kingSaudRdTxt>", "theAvenuesKMText": "<theAvenuesKM>4 KM</theAvenuesKM>", "kingAbdulRoadText": "<kingAbdulRdTxt>King Abdulaziz Road</kingAbdulRdTxt>", "alsalamHospitalText": "<alsalamHospitalTxt>Alsalam Hospital</alsalamHospitalTxt>", "kingAbdulazizStText": "<kingAbdulStTxt>King <PERSON><PERSON><PERSON> st</kingAbdulStTxt>", "princeAhmedStV1Text": "<princeAhV1StTxt>Prince <PERSON> st</princeAhV1StTxt>", "princeAhmedStV2Text": "<princeAhV2StTxt>Prince <PERSON> st</princeAhV2StTxt>", "saudiRoyalCourtText": "<saudiCourtTxt>Saudi Royal Court</saudiCourtTxt>", "alsalamHospitalKMText": "<alsalamHospitalKM>9 KM</alsalamHospitalKM>", "custodianHolyRoadText": "<custodianHolyRdTxt>Custodian of the Two Holy Mosques Road</custodianHolyRdTxt>", "kingAbdulazizPortText": "<kingPortTxt>King Abdulaziz Port</kingPortTxt>", "saudiRoyalCourtKMText": "<saudiCourtKM>4 KM</saudiCourtKM>", "kingAbdulazizPortKMText": "<kingPortKM>16 KM</kingPortKM>", "dammamRailwayStationText": "<dammamRailwayTxt>Dammam Railway Station</dammamRailwayTxt>", "dammamRailwayStationKMText": "<dammamRailwayKM>11 KM</dammamRailwayKM>", "greenSportsHallsOfDammamText": "<greenSportsTxt>Green Sports Halls of Dammam</greenSportsTxt>", "greenSportsHallsOfDammamKMText": "<greenSportsKM>10 KM</greenSportsKM>", "princeSaudBinJalawiStadiumText": "<princeJalawiStadiumTxt>Prince <PERSON><PERSON></princeJalawiStadiumTxt>", "kingFahdInternationalAirportText": "<kingFahdAirportTxt>King Fahd International Airport</kingFahdAirportTxt>", "princeMohammadBinFahdStadiumText": "<princeFahdStadiumTxt>Prince <PERSON> Stadium</princeFahdStadiumTxt>", "princeSaudBinJalawiStadiumKMText": "<princeJalawiStadiumKM>11 KM</princeJalawiStadiumKM>", "kingFahdInternationalAirportKMText": "<kingFahdAirportKM>50 KM</kingFahdAirportKM>", "princeMohammadBinFahdStadiumKMText": "<princeFahdStadiumKM>12 KM</princeFahdStadiumKM>", "imamAbdulrahmanBinFaisalUniversityText": "<imamUnivTxt>Imam <PERSON><PERSON><PERSON> University</imamUnivTxt>", "kingAbdulazizCenterForWorldCultureText": "<kingCultureCenterTxt>King Abdulaziz Center for World Culture - Ithra</kingCultureCenterTxt>", "kingAbdulazizCenterForWorldCultureKMText": "<kingCultureCenterKM>9 KM</kingCultureCenterKM>", "kingFahdUniversityOfPetroleumAndMineralsText": "<kingFahdUnivTxt>King <PERSON><PERSON>d University of Petroleum and Minerals</kingFahdUnivTxt>", "kingFahdUniversityOfPetroleumAndMineralsKMText": "<kingFahdUnivKM>10 KM</kingFahdUnivKM>"}, "ringRoad": "Ring Road", "airportRoad": "Airport Road", "sedra2Title": "<title>SEDRA 403</title>", "sedra3Title": "<title>SEDRA 3</title>", "sedra4Title": "<title>SEDRA 4</title>", "sedra5Title": "<title>SEDRA 5</title>", "almanarTitle": "<almanarTitleAttr>ALMANAR</almanarTitleAttr>", "northStation": "Riyadh North Station", "exploreSedra2": "<text>EXPLORE</text><n/><title>SEDRA 2</title>", "exploreSedra3": "<text>EXPLORE</text><n/><title>SEDRA 3</title>", "exploreSedra4": "<text>EXPLORE</text><n/><title>SEDRA 4</title>", "thoumamahRoad": "Thoumamah Road", "roshnFrontText": "<amenitiesText>ROSHN Front</amenitiesText>", "airportRoadText": "<airportAttr>Airport Road</airportAttr>", "thumamaRoadText": "<thumamaAttr>Thumama Road</thumamaAttr>", "metroStationText": "<amenitiesText>Metro Station</amenitiesText>", "almanarPhase1Title": "<almanar1Attr>ALMANAR Phase 1</almanar1Attr>", "parkAvenueMallText": "<amenitiesText>Park Avenue Mall</amenitiesText>", "alJanadiryahRoadText": "<alJanaAttr>Al Janadiryah Road</alJanaAttr>", "makkahJeddahRoadText": "<mekkahJeddahAttr><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></mekkahJeddahAttr>", "northernRingRoadText": "<northernAttr>Northern Ring Road</northernAttr>", "imamSaudiUniversityText": "<amenitiesText>Imam Saud University</amenitiesText>", "princessNouraUniversityText": "<amenitiesText>Princess <PERSON><PERSON></amenitiesText>", "kingSalmanBinAbdulazizRoadText": "<kingSalmanAttr>King <PERSON><PERSON> Bin <PERSON> Road</kingSalmanAttr>", "kingKhalidInternationalAirportText": "<amenitiesText>King Khalid International Airport</amenitiesText>"}, "health": {"center": "Bryman Medical Center", "company": "Healthcare Development Holding Company", "service": "AL mutabagani health service", "complex1": "King <PERSON> Medical Complex", "complex2": "<PERSON> General Medical Complex", "hospital1": "King Faisal Specialist Hospital", "hospital2": "Mediclinic Al Murjan Hospital", "hospital3": "Saudi German Hospital"}, "mosque": {"mosque1": "al kawthar slaba mosque", "mosque2": "Al arahmah mosque", "mosque3": "Corniche mosque", "mosque4": "kaia masjid", "mosque5": "Rida mosque", "mosque6": "Al masjid", "mosque7": "Island Mosque"}, "retail": {"mall1": "aya mall", "mall2": "REd sea mall", "mall3": "Heraa International Mall", "mall4": "Mall of Arabia"}, "landmark": {"square": "Altawheed Square", "statue": "JED statue", "airport": "King Abdulaziz International Airport", "stadium": "King <PERSON> Sport City Stadium", "sculpture1": "aya sculpture", "sculpture2": "Sculpture 'Magic Carpet'"}, "education": {"academy": "Jeddah world academy", "school1": "Talia Academy school", "school2": "Jeddah International Schools", "school3": "Jeddah Private International Schools", "school4": "Yusr International School"}, "distanceCircle": {"km": "km"}}}, "communities": {"amenities": {"caption": "Explore amenities in detail by clicking each card"}, "aDayInLife": {"title": {"sedra": "THE NEXT PHASE OF SEDRA", "warefa": "THE NEXT PHASE OF WAREFA", "alarous": "A day in the life in ALAROUS"}}, "masterplan": {"title": "A vibrant township that cultivates harmonious connections", "explore": "Explore our strategic location", "subtitle": "ALAROUS is situated to the north of Jeddah, south of King Abdullah Sports City, and north of King Abdulaziz International Airport. Its strategic location offers convenient access to the city's major landmarks and main roads. The township enables residents to mobilise anywhere in the community within walking distance.", "interactiveMasterplan": "Interactive masterplan"}, "philosophy": {"title": {"sedra": "The ROSHN Approach", "warefa": "The ROSHN Approach", "alarous": "Our Philosophy"}}, "homeSection": {"book": "SCHEDULE A VISIT", "caption": "Visit our sales centre from 10:00 AM to 7:00 PM and explore our offerings in person!", "explore": "Buy your home now", "registerInterest": "Register your interest"}, "propertyCard": {"3dView": "3D", "features": "FEATURES", "waitlist": "Coming soon", "squareUnit": "sqm", "virtualTour": "VIRTUAL TOUR", "startingFrom": "Prices start from:", "lowAvailability": "Low Availability", "orientationSize": {"E": "E", "N": "N", "S": "S", "W": "W", "NE": "NE", "NW": "NW", "SE": "SE", "SW": "SW", "undefined": "-"}, "priceDisclaimer": "*Prices are subject to availability and location", "bottomDisclaimer": "*Prices are subject to availability", "orientationTitle": "Unit Orientation"}, "communityBrief": {"brochure": "Brochure", "phasesInTotal": "phases in total"}, "reserveDreamHome": {"title": "Buy your dream home now!", "button": "Sign up", "reasons": {"caption": "Sign up to access features that allow you to:", "reason1": "See all available homes and prices", "reason2": "Reserve and then purchase your property online", "reason3": "Manage all sales documents in one place"}}, "registerYourInterest": {"title": "Register your interest", "submitButton": "Register", "successMessage": "Thank you for registering your interest"}}, "homeFinance": {"over": "over", "years": "years", "clickHere": "click here", "carFinance": "Car Finance installment", "howItWorks": "How it works?", "toPurchase": "to purchase a property worth", "downpayment": "Down payment", "fromTheBank": "from the banks", "howItWorkItems": {"step1": "Select a preferred home finance provider and download the sales offer (the provider can be changed later).", "step2": "Contact the selected home finance provider and submit your application for Home Financing using the sales offer.", "step3": "After a mutual agreement between you and the home finance provider, the provider will take care of managing the installments on your behalf."}, "netMonthSalary": "Net salary after GOSI deduction", "noteCalculator": "Please note that our calculator provides estimations and may not reflect the final offer. The final offer is subject to the finance provider's policy.", "sakaniQuestion": "Are you a Sakani program beneficiary?", "showStickyNote": "false", "financeDuration": "Finance period", "personalFinance": "Personal finance installment", "stickyNoteTexts": {"group": "ROSHN Group", "message": "In our ongoing efforts to improve your experience, ROSHN App will  be conducting a scheduled outage for maintenance purposes on Friday, 23rd of August 2024 from 02:00 AM until 05:00 AM.", "greeting": "Dear ROSHN Customer,", "appreciation": "We appreciate your understanding, and strive to serve you better!"}, "totalObligation": "Total monthly financial obligations", "otherInstallment": "Others monthly installment", "bankingObligagtion": "Banking obligations", "calculatorSubTitle": "This calculator is for illustrative purposes only and accuracy of any of the information obtained is not guaranteed. The values and figures shown are exclusive of any applicable tax and are hypothetical and may not be applicable to your individual situation. This calculator does not have the ability to pre-qualify you for any home finance. Be sure to consult your home finance provider to receive the applicable offer.", "firstHouseQuestion": "Are you a first home buyer?", "monthlyInstallment": "Monthly installment", "visitSakaniWebsite": "Visit Sakani website for details", "withMonthlyPayment": "with a monthly payment of", "borrowingFromFriend": "Borrowing from friends, relatives or others installment", "durationDescription": "The finance duration depends on the policy of the finance provider", "financeFromEmployer": "Finance from Employer installment", "howItWorkDescription": "In order to estimate your eligibility, each bank refers to a Stress Test Rate, currently averaging across banks about 4% (the rate used in the calculator). The rate varies between 4% and 8.5% depending on the Bank and its policy. The stress test rate tend to consider the following components: Saudi Interbank Offered Rate, a margin to cover for volatility in the Saudi Interbank Offered Rate over the period of the loan, the net margin of the bank, loan and property insurance.", "nonBankingObligation": "Non Banking obligations", "totalMonthlyFinancal": "Total monthly financial obligations", "firstHouseDescription": " ", "yourMortgageEstimates": "Your mortgage estimates", "estimateYourHomeFinance": "Estimate your home finance", "increaseDownpaymentAlert": "It seems like the property you’re interested in might be a bit out of your price range based on our calculations. Please consider increasing your down payment to make it more affordable.", "interestedInaFinancially": "To know your home finance eligibility click here", "yourEstimatedHomeFinance": "Your estimated home finance", "netMonthSalaryPlaceholder": "Enter your net salary", "noteCalculatorReservation": "Please reserve the unit and select your home finance provider within our app to proceed. Once done, we'll provide you a sales offer. We suggest contacting your bank to apply for home finance.", "totalObligationPlaceholder": "E.g. Finance, credit cards, etc.", "yourMortgageEstimatesSubText": "You can adjust the values below to see dynamic information.", "youCanFinanceAMaximumAmountOf": "You are eligible for the following amount", "youWantToKnowYourMaxHomeFinancingAmount": "To know your home finance eligibility "}, "nafathError": {"address": {"cta": "Update national address", "phone": "920 022 288", "step1": "1. Log in to your SPL account.", "step2": "2. Update your National address and ensure your details are complete and up to date.", "step3": "3. Once updated, retry verifying your account again. It might take up to 30 minutes to update your profile information.", "title": "Action Required: Missing\nNational Address", "paragraph1": "It seems that your <b> National address </b> is missing. To complete the account verification process, please follow the following steps:", "paragraph2": "For assistance, feel free to contact ROSHN Customer Care."}, "generic": {"phone": "920 022 288", "title": "Missing Information for\nAccount Verification", "paragraph1": "It seems that some required information is missing from your account. Please update your details to complete the verification process.", "paragraph2": "For assistance, feel free to contact ROSHN Customer Care."}, "timeout": {"phone": "920 022 288", "title": "The session has timed out", "paragraph1": "It seems that your session has expired. Please try again to continue the verification process.", "paragraph2": "If the issue persists, contact our ROSHN Customer Care for assistance."}, "somethingWentWrong": {"phone": "920 022 288", "title": "Something went wrong", "paragraph1": "An unexpected error occurred. Please refresh the page or try again later.", "paragraph2": "If you continue to experience issues, please reach out to ROSHN Customer Care."}}, "myProperties": {"edit": "Edit", "status": {"draft": "Draft", "reserved": "Reserved", "cancelled": "Cancelled", "purchased": "Purchased"}, "parking": "Parking spaces", "property": "Property", "kitchenTag": "Choose kitchen Colour", "myProperties": "My Properties", "noProperties": "You have no properties", "salesAdvisor": "Sales Advisor", "propertyDetail": {"steps": {"view": "View", "finance": {"save": "Save", "title": "Finance detail", "confirm": "Confirm the funding", "salesOffer": "Sales Offer", "notSelected": "Not selected", "paymentPlan": "Payment plan", "bankSelected": "Bank selected", "switchToCash": "Switch to Cash", "downpaymentDue": "Total Due Downpayment", "reservationFee": "Reservation Fee", "switchToHomeFiance": "Switch to Home finance", "remainingDownpayment": "Remaining Downpayment"}, "inspect": {"title": "Inspection & Handover"}, "nextStep": "Next step: Get your funding by ETA Q3 2026", "purchase": {"note": {"cash": "Within the **next 7 working days, you need to complete the down payment**. Once your payment is confirmed, you will receive the **executed Sales and Purchase Agreement (SPA)**.", "remind": "Please ensure that this and later transfers originate from your own bank account. If you wish to transfer from a 3rd party account, please contact your sales advisor to sign No Objection Certificate.", "homeFinance": "Within the **next 15 working days, you must sign the Home Finance Agreement with your bank**. After that, your bank will have another 15 working days to transfer the down payment to ROSHN. Once the payment is received from your bank, you will receive the **executed Sales and Purchase Agreement (SPA)**. Please get in touch with your bank to proceed with your home financing process."}, "sign": "Sign Sales and Purchase Agreement (SPA)", "title": "Purchase detail", "payment": "Payment milestones", "complete": "Complete the down payment", "goToMyProperty": "Go to my properties", "downloadSupplement": "Supplemental Agreement", "salesPurchaseAgreement": "Draft Sales and Purchase Agreement (SPA)"}, "viewDetail": "View Details", "attachments": "Attachments", "reservation": {"title": "Reservation detail", "payfee": "Pay the reservation fee", "review": "Review & E-sign agreement", "complete": "Complete personal infomation", "worldCheckMessage": "Thank you for choosing ROSHN!, Our team will review your reservation within 5 working days to confirm the reservation status of unit", "kitchenPreferences": {"date": "December 31st", "title": "For a limited time until ", "title2": "You can choose the preferred colour of your kitchen at no cost. Make your selection now!", "viewDetail": "View Details", "personalize": "PERSONALIZE NOW", "kitchenConfirmedMessage": "Your kitchen colour selection has been successfully updated."}, "reservationReceipt": "Reservation Receipt", "continueReservation": "Continue Reservation", "unitReservationForm": "Unit Reservation From", "kitchenPreferenceDocument": "Letter of Commitment - Kitchen Preferences"}, "paymentMilestone": {"dueAt": "Upon {{percent}}% completion", "title": "Payment milestones", "downpayment": "Down payment paid"}, "kitchenColourSelection": "Kitchen Colour Selection"}, "nextStep": "Next Step", "documents": {"download": "Download", "documents": "Documents", "documentNotReady": "The document is not yet ready. If you have any question, please contact us."}}, "kitchenSubtitle": "Personalise your kitchen for eligible property", "noPropertiesCTA": "Explore our residential units", "meetYourSalesAdvisor": "Meet your sales advisor", "meetYourSalesAdvisorDescription": "In case you need any assistance in your purchase journey, please contact your sales advisor"}, "setupAccount": {"step1": "Nafath Verification", "step2": "Email Verification", "accountSetup": "Complete your account setup", "accountSetupMsg": "To fully activate your account, please complete the following step(s):", "verifyAccountBtn": "VERIFY MY ACCOUNT"}, "nafathIdExpire": {"p1": "It seems that your identification has expired. Please renew your identification to continue using our services.", "cta": "Verify now", "title": "Expired Identification Notice"}, "propertyFinder": {"add": "Add", "buy": "BUY", "for": "for", "sar": "", "sqm": "sqm", "beds": "Beds", "done": "Done", "more": "more", "over": "Over", "rent": "RENT", "sold": "Sold", "wind": "Wind", "baths": "Baths", "notes": "Notes", "rooms": "ROOMS", "roshn": "Roshn", "title": "find your property", "under": "Under", "years": "years", "3dView": "3d View", "REGANo": "REGA Ad License No", "copied": "<PERSON>pied", "riyadh": "Riyadh", "search": "Search", "yearly": "yearly", "alarous": "Alarous", "endDate": "End Date", "forSale": "For sale", "mapView": "MAP VIEW", "message": "Your interest has been submitted, Brokerage team will contact you soon.", "results": "results", "seeMore": "See More", "bedrooms": "Bedrooms", "brochure": "Brochure", "copyLink": "Copy Link", "features": "FEATURES", "forLease": "For lease", "listView": "LIST VIEW", "listings": "Listings", "location": "LOCATION", "overview": "Overview", "plotArea": "PLOT AREA", "projects": "Projects", "reachOut": "REACH OUT", "resetAll": "RESET ALL", "tryAgain": "Try Again", "amenities": "Amenities", "available": "Available", "bathrooms": "Bathrooms", "contactUs": "Contact Us", "developer": "Developer", "floorPlan": "Floor plan", "floorplan": "Floor plans", "moreHomes": "More Homes", "ourVision": "OUR VISION", "searchBar": {"cities": {"title": "City", "options": {"jeddah": "Jeddah", "makkah": "<PERSON><PERSON><PERSON>", "riyadh": "Riyadh", "dhahran": "<PERSON><PERSON><PERSON><PERSON>"}}, "bedrooms": {"title": "Bedrooms", "options": {"any": "Any"}, "placeholder": "Add numbers"}, "bathrooms": {"title": "Bathrooms", "options": {"any": "Any"}, "placeholder": "Add numbers"}, "unitTypes": {"title": "Type", "options": {"any": "Any", "villa": "Villa", "duplex": "Duplex", "townhouse": "Townhouse"}, "placeholder": "Choose unit types"}, "unitSource": {"title": "Unit Source", "options": {"CRM": "CRM", "any": "Any", "ZILLOW": "ZILLOW", "SHOPOXO": "SHOPOXO"}}, "communities": {"title": "Where", "cities": {"jeddah": "Jeddah", "riyadh": "Riyadh"}, "options": {"any": "Any", "sedra": "SEDRA", "warefa": "WAREFA", "alarous": "ALAROUS"}, "results": "Location Results", "mobileTitle": "Location", "placeholder": "Select communities", "allPlaceholder": "All Communities", "popularLocations": "Popular Locations"}, "sortByPrice": {"title": "Price", "options": {"any": "Any", "asc": "Low to High", "desc": "High to Low"}}}, "startDate": "Start Date", "viewOnMap": "view on map", "loanPeriod": "Loan Period", "masterPlan": "Master Plan", "masterplan": "MasterPlan", "minMaxArea": "{{minArea}} sqm - {{maxArea}} sqm", "newRelease": "New Release", "onHandover": "On handover", "ourMission": "OUR MISSION", "priceRange": "Price Range", "properties": "properties", "refundable": "Refundable", "retalTitle": "Retal Real Estate", "roomsValue": "1-7 Bedrooms, 2-8 Bathrooms", "totalPrice": "Total price", "unitNumber": "Unit Number", "windLegend": "Wind Legend", "amountToPay": "Amount to pay", "contactText": "We’re here to help! Reach out and we’ll get back to you as soon as possible.", "downPayment": "Down Payment", "exploreUnit": "Explore unit", "goToMapView": "go to map view", "homesForYou": "Homes for you", "paymentPlan": "PAYMENT PLAN", "paymentType": "Payment Type", "placeholder": "Community, Type, Bedroom and Bathroom", "temperature": "Temperature", "viewDetails": "view details", "viewProfile": "View Profile", "weatherData": "Weather data", "aboutProject": "About the project", "exploreUnits": "Explore Available Units", "falLicenseNo": "FAL License No:", "imagePreview": "Image Preview", "interestRate": "Interest Rate", "ofTotalPrice": "of total price", "resultsFound": "results found", "searchButton": "Search", "thisProperty": "THIS PROPERTY", "warrantyCard": "Warranty Details", "locationOnMap": "LOCATION", "minMaxBedBath": "{{minBed}}-{{maxBed}} Bedrooms, {{minBath}}-{{maxBath}} Bathrooms", "plotAreaValue": "10,000 sqm - 50,000 sqm", "precipitation": "Precipitation", "projectAssets": "Project assets", "propertyTypes": "Property Types", "backHomeButton": "Back to home", "developerTitle": "<PERSON><PERSON>", "exploreProject": "Explore Project", "findHomeButton": "Find a home", "noResultsFound": "No results found", "reservationFee": "Reservation Fee", "buyThisProperty": "THIS PROPERTY", "commercialUnits": "Commercial Units", "lowAvailability": "Low Availability", "ofPropertyPrice": "of property price", "priceRangeValue": "SAR 500,000 - 900,000", "retalProperties": "RETAL PROPERTIES", "thankYouMessage": "Thank you for reaching out to us! We have received your request and will contact you using the communication details you provided.", "climateRiskLevel": "Climate Risk Level", "contactDeveloper": "Contact Developer", "mortgageEstimate": "MORTGAGE ESTIMATE", "pPerInstallation": "%per installation", "residentialUnits": "Residential Units", "seeAllProperties": "See all properties", "showLessFeatures": "Show less features", "showMoreFeatures": "Show more features", "startReservation": "reserve", "backToListingPage": "Back to Listing page", "copiedToClipboard": "Link has been copied to clipboard!", "developerLocation": "Riyadh, Saudi Arabia", "loadingProperties": "Loading Properties...", "maidsRoomIncluded": "Maid’s room included", "priceNotAvailable": "Price Not Available", "temperatureLegend": "Temperature Legend", "averageTemperature": "Average Temperature", "duringConstruction": "During Construction", "features&Amenities": "Features & Amenities", "mortgageCalculator": "Mortgage Calculator", "propertyTypesValue": "Villa, Duplex, Townhouse", "samplePropertyInfo": "Roshn offers a variety of unit prototypes consisting of four or five bedrooms with variety of spaces.", "browseSelectedUnits": "Browse selected units", "editMortgageDetails": "EDIT MORTGAGE DETAILS", "listingErrorHeading": "Couldn't fetch properties", "precipitationLegend": "Precipitation Legend", "propertyInformation": "Property Information", "zeroPriceValidation": "Total price must be greater than 0", "RegisterYourInterest": "Register Your Interest", "developerDescription": "Retal real estate is a leading real estate developer based in Saudi Arabia, committed to shaping modern urban lifestyles. With a focus on innovative design, quality construction and some other data", "loanPeriodValidation": "Loan period must be between 5 and 30 years", "totalPriceValidation": "Please enter a valid price. Maximum is based on the selected unit", "calculatorDescription": "This calculator is for illustrative purposes only and accuracy of any of the information obtained is not guaranteed. The values and figures shown are exclusive of any applicable tax and are hypothetical and may not be applicable to your individual situation. This calculator does not have the ability to pre-qualify you for any home finance. Be sure to consult your home finance provider to receive the applicable offer.", "downPaymentValidation": "Down payment must be between 20% and 80% of the unit price", "interestRateValidation": "Interest rate must be between 1% and 10%", "submissionUnsuccessful": "Submission Unsuccessful!", "listingErrorDescription": "Error loading properties listing data. Please try again", "thankYouCardDescription": "Thank you for trusting us with your property and submitting your request. We will review it and contact you soon!", "commercialRegistrationNo": "Commercial Registration No.", "eligibleAmountDescription": "You are eligible for the following amount", "monthlyPaymentDescription": "Your monthly payment will be", "selectWeatherDataAmenities": "Select Weather Data / Amenities", "requestSubmittedSuccessfully": "Request submitted successfully!", "interestedInBuyingThisProperty": "Interested in buying this property? Reach out to us, and our agents will be happy to help.", "somethingWentWrongPleaseTryAgain": "Something went wrong, please try again.", "noPropertiesFoundMatchingYourCriteriaTryAdjustingYourFiltersAndTryAgain": "No matching properties found. Try adjusting your filters, or register your interest, and our support team will reach out to help you find the best options that suit your needs!"}, "premiumResident": {"p1": "Thank you for your interest in reserving a property with us. To proceed, you need to hold a Premium Residency in Saudi Arabia.", "p2": "Please visit the <b> Premium Residency</b> website to learn more", "p3": "If you have any questions, please contact ROSHN customer care:", "cta": "CONTACT US", "phone": "920 022 288", "title": "Premium Residency Required", "tooltipCta": "PREMIUM RESIDENCY WEBSITE", "tooltipContent": "For more information or applying for a premium residency please visit the website."}, "resignDocuments": {"docs": {"SPA": "Sales Purchase Agreement", "SALES_OFFER": "Sales Offer", "SECOND_SUPL_AGMT": "Second Supplemental Agreement", "UNIT_RESERVATION_FORM": "Unit Reservation Form"}, "appBar": {"cta": "REVIEW PENDING ACTIONS", "message1": "You have outstanding items awaiting your review and e-signature. Kindly address them at your earliest convenience.", "actionRequired": "Action Required"}, "thankYou": {"cta": "DONE", "heading": "Thank you!", "message": "We appreciate your prompt attention to this matter! Your action required documents have been successfully updated. "}, "documentModal": {"cta": "RESIGN DOCUMENTS", "otp": "Confirm your identity to e-sign", "heading": "Please complete pending actions", "message": "There have been some updates to your documents. Kindly continue e-signing all your updated documents to complete the process", "subHeading": "Which documents need to be reviewed and e-signed?", "actionRequiredMsg": "You have outstanding items awaiting your review and e-signature. Kindly address them at your earliest convenience."}, "pendingAction": {"cta": "REVIEW PENDING ACTIONS", "skip": "SKIP NOW", "heading": "Please complete pending actions", "message": "There have been some updates to your documents. Kindly continue resigning all your updated documents to complete the process. "}, "propertyDetailBanner": {"cta": "E-SIGN DOCUMENTS", "docType": "Please review and e-sign your {{docType}}", "heading": "Action Required", "message1": "You have ", "message2": " outstanding items awaiting your review and e-signature. Kindly address them at your earliest convenience", "firstContent": "Please Review and Resign Your"}}, "emailVerification": {"cta": "VERIFY MY ACCOUNT", "title": "Complete your account setup", "message": "To fully activate your account, please complete the following step(s):", "enterOTP": {"in": "in", "cta": "CONFIRM CODE", "error": {"expiredOTP": "OTP expired! Please try the RESEND button for a new one.", "invalidOTP": "Invalid OTP. Please check the code and try again.", "maxAttempts": "Maximum number of login attempts exceeded"}, "title": "Verify email", "resend": "Resend", "message": "Please enter the 6-digit code sent to your email"}, "editEmail": {"cta": "CONTINUE", "error": {"cta": "TRY AGAIN", "title": "Verification failed!", "message": "Something went wrong, please try again."}, "title": "<PERSON><PERSON> Verified successfully", "message": "Thank you for confirming your email account"}, "enterEmail": {"cta": "CONTINUE", "error": {"message": "Please enter a valid email address"}, "label": "Please enter your email account for verification", "terms": "I acknowledge and approve that my details provided previously to the Developer in any Sale and Purchase Agreements will be updated based on the information received from <PERSON><PERSON>th or provided by me. I further acknowledge and approve that all upcoming communications for all Sale and Purchase Agreements, such as but not limited to: invoices, notices and announcements, issued by the Developer will be sent to most recent contact information received by the Customer.", "title": "Verify email", "editModeLabel": "Please enter your new email", "editModeTitle": "Edit email", "emailPlaceHolder": "Enter your email"}, "emailVerify": "Email Verification", "nafathVerfiy": "Nafath Verification", "verificationFailed": {"message": "Email verification failed, please try again."}, "verificationSuccess": {"cta": "CONTINUE", "title": "Account verified successfully", "message": "Thank you for verifying the account. Your account is now fully active.\nPlease proceed with the reservation."}}, "propertyDiscovery": {"back": "BACK", "unit": "Unit", "share": "share", "start": "Start", "title": "Make your selection to complete the personalisation of your home.", "bedroom": "Bedrooms", "bathroom": "Bathrooms", "continue": "Continue Reservation", "landArea": "Land area", "location": "Location", "plotArea": "Plot area", "amenities": "Amenities", "furnished": "Furnished", "yearBuilt": "Year Built", "exterior3d": "EXTERIOR 3D", "interior3d": "INTERIOR 3D", "milestones": "MILESTONES", "selectUnit": "Select Property", "bookViewing": "BOOK APPOINTMENT", "builderName": "Builder Name", "builtUpArea": "Built-up area", "estDelivery": "Est. Delivery end of", "orientation": "Orientation", "paymentPlan": {"cash": "Cash", "title": "Payment Plan", "homeFinancing": "Home financing"}, "roofTerrace": "Roof terrace", "soldOutNote": "This unit is currently sold out on this phase, please register your interest to get the earliest update for its availability", "viewDetails": "View Details", "findYourHome": "Find your home", "listTeamName": "List Team Name", "maidIncluded": "Maid's room included", "checkLocation": "Check location", "keepMeUpdated": "Keep me updated", "priceExclRETT": "Price excl. RETT", "unitAmenities": {"cabin": "Cabinets", "title": "Unit amenities", "acUnit": "A/C unit", "balcony": "Balcony", "kitchen": "Kitchen installed", "parking": "Parking for {{parking}} cars", "accessibility": "Accessibility"}, "viewCommunity": "View community", "directionFaces": "Direction Faces", "driverIncluded": "Driver's room included", "fullScreenMode": "Full screen mode", "grossFloorArea": "Gross floor area", "propertyDetail": "Property details", "selectYourHome": "Select your home", "standardStatus": "Standard Status", "additionalRooms": "Additional rooms", "cashPaymentPlan": "Cash payment plan", "exploreYourHome": "Explore your home", "paymentPlanNote": "The timing and number of payments are based on completion status", "reserveYourHome": "reserve your home", "unitHoldMessage": "This Unit is held for you, kindly proceed with the next steps to reserve it. The remaining time for the hold is: ", "buyingLikelyHood": "Likelihood to buy", "listingAgreement": "Listing Agreement", "downLoadFloorPlans": "Download floorplans", "maidDriverIncluded": "Maid's and driver's rooms included", "communityAmentities": {"title": "Community amenities"}, "interiorVirtualTour": "Interior Virtual Tour", "propertyInformation": "Property Information", "associationamenities": "Association Amenities", "patioAndPorchFeatures": "Patio And Porch Features", "officeCorporateLicense": "Office Corporate License"}, "exploreCommunities": {"title": "Explore communities", "exploreButton": "Explore {{community}}"}, "kitchenPreferences": {"note": "*Please note that the color representation in this image may vary slightly. For an accurate assessment, we encourage you to visit our sales center and examine the actual sample.", "title": "Kitchen Preferences", "loader": {"sign": "Please wait while your document is being signed", "saveColor": "Please wait while kitchen colour is being saved"}, "confirm": "Confirm your identity to e-sign", "heading": "Make your selection to complete the personalisation of your kitchen colour", "continue": {"title": "Thank you!", "button": "CONTINUE", "description": "Thank you for making your selection! We're excited to confirm that your kitchen preferences have been successfully registered."}, "description": "You can choose the colour of your top cabinets. See which combinations works for you with our signature bottom wood cabinetry.", "agreementCta": "Review the reservation", "confirmPopup": {"title": "Confirm selection", "backButton": "Go back", "description": "Please note, your selection is final once confirmed. Do you want to continue?", "confirmButton": "Confirm"}, "kitchenColor": {"BLANCO_POLAR_SM_WHITE": "Blanco Polar SM - White", "VERDE_SALVIA_SM_GREEN": "Verde Salvia SM - Green", "ZENIT_GRIS_NUBE_SM_GREY": "<PERSON><PERSON> SM - Grey"}, "confirmButton": "Confirm selection", "documentTitle": "Review your reservation agreement", "documentCosent": "I consent to use of my personal information for the digital signature generation with emdha Trust Service Provider.", "fullScreenButton": "Full screen mode", "confirmButtonInfo": "Finish your selection to continue.", "documentEsignKitchen": "E-Sign Commitment", "documentKitchenTitle": "Review Letter of Commitment", "documentConsentButton": "E-Sign the purchase agreement"}, "somethingWentWrong": {"title": "Something went wrong!", "primaryCta": "CONTACT US", "description": "Sorry, there was a problem. please try again"}, "propertyReservation": {"hour": "H", "step": {"step1of3Next": "Up next: Review payment information", "step2of3Next": "Up next: Complete your purchase", "step3of3Next": "Up next: Get your Sales and Purchase Agreement", "step1of3Title": "Review reservation information", "step2of3Title": "Review payment information", "step3of3Title": "Complete your purchase", "step1of3Title1": "Financial information", "step1of3Description": "Step 1 of 3: Reserve your new home", "step2of3Description": "Step 2 of 3: Payment information", "step3of3Description": "Step 3 of 3: Complete your purchase"}, "minute": "M", "second": "S", "withIn": "within", "billing": {"city": "City", "save": "Save", "email": "Email", "country": "Country", "postcode": "Post code", "cityError": "Please enter your city", "firstName": "First name", "emailError": "Please enter your email", "familyName": "Family name", "addressError": "Please enter your address", "addressLine1": "Address Line 1", "lastNameError": "Please enter your family name", "postCodeError": "Please enter your postcode", "firstNameError": "Please enter your first name", "additionalAddress": "Additional Address"}, "funding": {"note": "You will need your sales offer to contact your selected home finance provider to receive a home finance offer.", "contact": "Please contact your home finance provider", "download": "Get your sales offer", "bankSelect": {"label": "Select your home finance provider", "placeholder": "Select the preferred home finance provider"}, "cashFunding": "Your payment method is cash", "remainingStep": "Please contact your home finance provider and submit your application for home financing using the sales offer.", "downloadButton": "Download your sales offer", "downpaymentDue": "Total Due Downpayment", "reservationFee": "Reservation Fee", "noteDownpayment": "Your detailed payment plan can be found within the SPA.", "reviewSaleOffer": "Review the sales offer", "eSignTheSaleOffer": "E-sign the sales offer", "remainingDownpayment": "Remaining Downpayment"}, "invoice": {"totalAmount": "Payable now", "reservationFee": "Reservation fee"}, "onboard": {"esign": "E-Sign", "button": "REVIEW DOCUMENT", "confirm": "Confirm your identity to e-sign", "consent": "I consent to use of my personal information for the digital signature generation with emdha Trust Service Provider.", "thankYouModal": {"button": "DONE", "heading": "Thank You", "message": "We're happy to confirm that your Unit Reservation & KYC Form has been e-signed successfully."}, "reviewAgreement": "Review Unit Reservation", "almanarInfoDialog": {"heading": "Sales and Purchase Agreement Not Available Yet", "message": "We will notify you as soon as it’s ready for signing. Once available, you can complete your reservation from the My Properties page. Thank you!", "primaryCta": "My Properties", "secondaryCta": "Contact Us"}}, "payment": {"or": "Or", "payNow": "Pay now", "dueToday": "Due today", "cvvNumber": "CVV number", "chargeType": "Charge type", "holderError": "Invalid card holder name", "totalAmount": "PAYABLE NOW", "paymentError": {"tryAgain": "Try Again", "contactUs": "Contact us", "errorMessage": "Something went wrong, payment can’t be completed. Please try again or contact us."}, "iAcceptToThese": "I agree to the ", "reservationFee": "Reservation fee", "payWithCreditCard": "Pay with Card", "termAndConditions": "Terms and Conditions", "paymentInformation": "Payment information", "completePaymentIn24h": "Reservation fee is fully refundable and if you complete the purchase, it will be credited towards your first installment.", "paymentLinkReference": "Payment link reference", "billingAddressMessage": "Please enter a valid billing address", "refundableDescription": "Total refundable reservation fees deductible from final price", "completePaymentIn24hOnFeeAdjustment": "The reservation fee is fully refundable upon payment of the first installment or upon cancellation."}, "thankyou": "Thank you for your first payment. We will provide regular updates on the status of your property.", "unitCard": {"RETT": "RETT", "price": "Price excl. RETT", "title": "Unit price", "description": "Please note that you are responsible for paying a maximum of 5% RETT on the total value of your property within the handover period."}, "cancelNote": "The reservation fee is refundable. To cancel the reservation and request a refund please contact the sales team.", "previewSPA": {"spa": "Draft Sales and Purchase Agreement", "title": "Preview documents", "download": "Download", "loadingTitle": "Please wait while the Sales and Purchase Agreement is being prepared", "successMessage": "Downloaded the document"}, "cashMessage": "Please ensure that this and later transfers originate from your own bank account. If you wish to transfer from a 3rd party account, please contact your sales advisor to sign No Objection Certificate.", "information": "You can buy your villa online or visit our sales center within the next 15 days to finalize your purchase.", "paymentPlan": {"1st": "1st installment", "2nd": "2nd installment", "3rd": "3rd installment", "4th": "4th installment", "5th": "5th installment", "6th": "6th installment", "7th": "7th installment", "8th": "8th installment", "9th": "9th installment", "dueAt": "Upon {{percent}}% completion", "fromTotal": "{{percent}}% of property price", "onHanover": "On Handover", "includeTax": "Final price incl. VAT", "totalPrice": "Total price excl. RETT", "downPayment": "Down payment", "firstAmount": "First payment amount to be paid:", "homeFinance": "Home finance", "installment": "Installment", "paymentPlan": "Payment plan", "description1": "Please note that our calculator provides estimations and may not reflect the final offer. The final offer is subject to the finance provider's policy.", "description2": "The numbers of payments are based on completion status", "paymentPlanDescription": "Please note that RETT is not included in the payment plan."}, "reservation": "Reservation", "downloadText": "Download your Unit Reservation Agreement", "goBackButton": "Go Back", "reReserveBtn": "Back to Masterplan", "purchaseStart": {"title": "You are ready to sign your Sales and Purchase Agreement!", "description": "You are one step away from being a homeowner"}, "reviewAndSign": "Review & e-Sign with EMDHA", "billingAddress": "Billing address", "congratulation": "Congratulations!\n We are building your dream home", "getYourFunding": "Get your funding", "goToMyProperty": "Go to My Properties", "reviewDocument": {"esign": "E-Sign the Sales and Purchase Agreement", "confirm": "Confirm your identity to e-sign", "consent": "I consent to use of my personal information for the digital signature generation with emdha Trust Service Provider.", "getFinal": "Up next: Get your Sales and Purchase Agreement", "floorPlan": "Floorplans", "beingSigned": "Please wait 5 to 10 minutes while the Sales and Purchase Agreement is being signed", "description": "A Sales and Purchase Agreement (SPA) is a legal contract between the buyer and the seller, defining the terms of the deal. It covers key information like price, payment plan, and contingencies, keeping everyone on the same page and protected.", "reviewDocument": "Review the Sales and Purchase Agreement", "worldCheckBody": "Our team will review your reservation within 5 working days to confirm the reservation status of unit", "stepDescription": "Step 3 of 3: Complete your purchase", "worldCheckTitle": "Thank you for choosing ROSHN!", "transferInformation": "Transfer information", "purchaseAgreementNote": "Please ensure that this and later transfers originate from your own bank account. If you wish to transfer from a 3rd party account, please contact your sales advisor to sign No Objection Certificate.", "signPurchaseAgreement": "Sales and Purchase Agreement", "reviewPurchaseAgreement": "Review the Sales and Purchase Agreement"}, "sessionExpired": "Session Expired", "signatureInput": {"success": "You have successfully signed your Unit Reservation Agreement", "waiting": "Please wait while your document is being signed", "otpSubtitle": "We have sent the code to your mobile  <b><ltr>{{phoneNumber}}</ltr></b>, please enter the 4 digit code you received in SMS", "openInPDFViewer": "Open in PDF viewer"}, "successMessage": "You have successfully reserved your property", "timeRunningOut": "Time will almost run out, please continue the payment.", "financeYourHome": "Review payment information", "informationCash": "Please complete your purchase within the next 7 working days. A dedicated member of our sales team will be in contact to answer any questions you might have and support you along the process.", "reserveYourHome": "Reserve your new home", "reserveYourUnit": "Reserve your new home", "thankyouMessage": "Thank you for signing the Sales and Purchase Agreement(SPA)", "yourHomeSummary": "Your home summary", "purchaseYourHome": "Purchase your home", "reReserveMessage": "Your session has expired due to the time limit.  Please note, the same unit might no longer be available, check back after 30 minutes or select another unit.", "startReservation": "Start reservation", "decideYourFunding": "Decide your funding", "reservationNumber": "Reservation number", "completeThePurchase": "Complete your purchase", "continueReservation": "Continue reservation", "discountEligibility": {"title": "You might be qualified for the discount!", "checkList": "Photocopy of ID\nPower of attorney (POA)\nUnit reservation form if applicable\nPhotocopy of ID (Authorized person in case of POA)\nFamily’s photo ID\nMOH registration\nEmployment letter from organization\nEmployment introduction letter\nDisability certificate if applicable\nRETT", "description": "You may be eligible for government or other assistance based on your employment status or personal situation. We're here to help! Simply get in touch with our sales centre.", "subDescription": "To help you check the discount eligibility, you might need the following documents for your appointment:", "bookAnAppointment": "Book an appointment"}, "personalInformation": {"name": "Name", "email": "Email", "phone": "Phone Number", "salary": {"question": "Monthly salary", "question1": "Total monthly financial obligations", "placeholder": "Enter your net salary", "errorMessage": "Please enter your salary", "placeholder1": "Eg: Finance, credit cards etc.", "tooltipMessage": "General organization for social insurance (GOSI)", "errorLowSalaryMessage": "Monthly Salary must be at least 3000 "}, "lastName": "Last name", "firstName": "First name", "offerDate": "Offer Date & Time", "nationalID": "National ID", "firstHomeBuyer": {"question": "Are you a first time home buyer?"}, "sourceOfIncome": {"question": "Source of income", "placeholder": "Select", "errorMessage": "Please enter your source of income"}, "financingMethod": {"cash": "Cash", "question": "How are you planning to buy?", "errorMessage": "Please enter your finance method", "homeFinancing": "Home finance"}, "sakaniBeneficiary": {"no": "No", "yes": "Yes", "button": "Visit Sakani website for details", "question": "Are you a Sakani beneficiary?", "errorMessage": "Please enter your Sakani option", "tooltipMessage": "Total monthly financial obligations (Personal Finance, Home Finance, Credit Card, Car Finance etc..)"}, "otherSourceOfIncome": {"question": "Other source of income", "placeholder": "Enter source of income", "errorMessage": "Please enter your other source of income"}, "personalInformation": "Personal Information", "totalMonthlyFinancal": {"question": "Total monthly financial obligations", "tooltipMessage": "Total monthly financial obligations (Personal Finance,\n Home Finance, Credit Card, Car Finance etc..)"}}, "propertyInformation": {"facade": "Facade type", "project": "Project", "updated": "Updated", "unitCode": "Unit code", "unitType": "Unit type", "floorPlan": "Floorplans", "subPhrase": "Sub phrase", "unitNumber": "Unit number", "neighborhood": "Neighborhood", "notStartedMessage": "New feature, Coming soon.", "saleRegisterNumber": "Sale register number", "propertyInformation": "Property information", "constructionProgress": "Construction Status", "constructionProgressCompleted": "Completed"}, "tellUsAboutYourSelf": "Tell us a bit about yourself", "thankyouForPurchase": "Thank you for signing the Sales and Purchase Agreement (SPA)", "addNewBillingAddress": "Add new billing address", "completeSignWithin30": "Within the next 15 working days you need to sign the Home Finance Agreement with your bank. After that, within the following 15 working days, your bank should transfer the down payment to ROSHN. Once they have done so, you will receive your Sales and Purchase Agreement. To make this happen, please contact your bank and discuss your home financing with them.", "completeYourPurchase": "Complete your purchase", "paymentCongratsTitle": "Congrats! You've successfully reserved your home", "selectADifferentUnit": "Select a different unit", "updateBillingAddress": "Update billing address", "useMyPersonalAddress": "Use my national address as a billing address", "downloadYourDocuments": "Download your documents", "employmentInformation": {"title": "Employment Information", "jobTitle": {"question": "Job title", "placeholder": "Enter job title", "errorMessage": "Please enter your Job title"}, "workSector": {"question": "Work Sector", "placeholder": "Select", "errorMessage": "Please enter your work Sector"}, "employerName": {"question": "Employer name", "placeholder": "Search by employer name", "errorMessage": "Please enter your employer name"}, "otherEmployerName": {"question": "Other employer name", "placeholder": "Enter other employer name", "errorMessage": "Please enter your other employer name"}}, "startReservationError": {"contactUs": "CONTACT US", "updateEmail": "Update Email", "emailMissing": "Email Address Needed", "viewMyProperties": "VIEW MY PROPERTIES", "updateEmailMessage": "To proceed with your reservation, kindly update your email address.", "reservationLimitTitle": "The number of purchases allowed per community has been reached", "reservationLimitMessage": "Due to high levels of demand for our homes, if you wish to purchase more than three units, please call the ROSHN customer care center on ********* to submit your request."}, "informationHomeFinance": "Please complete your purchase within the next 15 working days. A dedicated member of our sales team will be reaching out to you soon to answer any questions you might have and support you along the process.", "reservationInformation": "Reservation information", "tranferInformationCard": {"IBAN": "IBAN:", "dueBy": "Payment due by:", "title": "Transfer information", "bankName": "Bank name:", "amountDue": "First payment amount to be paid:", "accountOwner": "Account owner name:"}, "signAgreementToPurchase": "Sign agreement to purchase", "reviewPaymentInformation": "Review payment information", "cancelNoteOnFeeAdjustment": "The reservation fee is fully refundable upon payment of the first installment or cancellation. For any support, please contact your assigned Sales Advisor or ROSHN Customer Care at ********* .", "downloadPurchaseAgreement": "Download Sales and Purchase Agreement", "paymentCongratsDescription": "You can buy your villa online or visit our sales center within the next 15 days to finalize your purchase.", "reservationTimerDescription": "Reservation fee required to finalize booking", "waitingForOnboardingMessage": "Please wait, do not refresh the page", "waitingForDetailsConfirmation": "Please wait while your details are being confirmed", "waitingForLongBackgroundProcess": "This may take some time, please wait", "youCanEasilyPurchaseYourHomeOnline": "You can easily purchase your home online", "youCanEasilyPurchaseYourHomeOnlineDescription": "In just a few clicks, you can reserve this property online by paying a 5,000  refundable reservation fee"}, "userAgeValidationDialog": {"cta": "Go Back", "title": "Age Requirement Not Met", "message": "You must be at least 18 years old according to the Hijri calendar to proceed with the reservation.\n Please try again once you meet the age requirement."}, "premiumResidentSalesCenter": {"p1": "Thank you for your interest in reserving a property with us. We kindly request that you visit our nearest sales center to complete your reservation.", "p2": "Please note that you will need <b> Premium Residency</b> to proceed with the reservation at the sales center. Learn more", "p3": "If you have any questions, please contact ROSHN customer care:", "cta": "Visit Sales Centre", "phone": "920 022 288", "title": "Reservation Assistance Needed", "tooltipCta": "PREMIUM RESIDENCY WEBSITE", "tooltipContent": "For more information or applying for a premium residency please visit the website."}, "UnableToVerifyPremiumResident": {"p1": "We are currently unable to verify your Premium Residency status.", "p3": "If you have any questions, please contact ROSHN customer care:", "cta": "CONTACT US", "title": "Unable to Verify Premium Residency"}, "premiumResidentRestrictCommunity": {"p1": "Thank you for your interest in the ALMANAR community! Please note that reservations in this community are open to Saudi nationals only. Feel free to explore other ROSHN communities where your future home awaits.", "p3": "If you have any questions, please contact ROSHN customer care:", "cta": "CONTACT US", "phone": "920 022 288", "title": "SAUDI NATIONALS ONLY"}}, "brokerage": {"registerInterest": {"own": "Own", "yearly": "Yearly", "monthly": "Monthly", "sixMonths": "Months", "doneButton": "Done", "errorTitle": "Submission unsuccessful!", "investment": "Investment", "secondHome": "Second Home", "step1Title": "Step 1 of 2: Property Preferences", "step2Title": "Step 2 of 2: Budget & Requirements", "drawerTitle": "Register your interest", "sedraRiyadh": "SEDRA - Riyadh", "rentProperty": "Rent a property", "submitButton": "Submit", "successTitle": "Request submitted successfully!", "warefaRiyadh": "WAREFA - Riyadh", "alarousJeddah": "ALAROUS - Jeddah", "aldanahDammam": "ALDANAH - Dam<PERSON><PERSON>", "almanarMakkah": "ALMANAR - <PERSON><PERSON><PERSON>", "selectCaption": "Select one or more options:", "continueButton": "Continue", "selectBedrooms": "Select the number of bedrooms you prefer", "selectCityRent": "In which city are you looking to rent?", "selectUnitType": "What type of unit are you looking for?", "tryAgainButton": "TRY AGAIN", "usagePlanLabel": "How do you plan to use your new home?", "moveInDateLabel": "When do you expect to move in?", "selectCommunity": "Which community are you interested in?", "budgetRangeLabel": "What’s your budget range for the new home?", "errorDescription": "Something went wrong, please try again.", "purchaseProperty": "Purchase a property", "drawerDescription": "Please fill in the following information to submit the form and we will get in touch with you.", "rentalBudgetLabel": "What’s your annual budget range for the new home?", "selectCityPurchase": "In which city are you looking to buy?", "successDescription": "Thank you for sharing your interest in ROSHN properties. We will review it and contact you soon!", "interestSelectLabel": "Select your interest", "propertyPreferences": "Property Preferences", "rentalDurationLabel": "How long do you plan to rent the property for?", "budgetAndRequirements": "Budget & Requirements", "furnishedPropertyLabel": "Do you require a furnished property?", "propertyManagementLabel": "Do you require property management service?"}}, "campaigns": {"summerFest": {"error": {"title": "Oops, entry unsuccessful!", "description": "Something went wrong, please try again!", "tryAgainCTA": "TRY AGAIN", "invalidEmail": "Oops! It looks like the email you entered isn't valid", "incorrectNumber": "The mobile number is incorrect", "invalidLastName": "Please provide valid last name", "invalidFirstName": "Please provide valid first name"}, "others": {"title": "Entry Already Submitted", "description": "You've already submitted your entry! \n You can submit another raffle entry in 24 hours window is up."}, "loginScreen": {"email": "Email", "phone": "Phone Number", "title": "One step away from winning big!", "iAgree": "I agree to the", "lastName": "Last Name", "firstName": "First Name", "iHaveRead": "I have read and agree to the", "description": "Share your details to unlock your chance to win.", "enterRaffle": "Enter The Raffle", "iAgreeRaffle": "I agree to the raffle", "privacyPolicy": "Privacy Policy", "emailPlaceholder": "Enter email", "phonePlaceholder": "Enter phone number", "termsAndConditions": "Terms & Conditions", "lastNamePlaceholder": "Enter last name", "firstNamePlaceholder": "Enter first name"}, "successScreen": {"email": "EMAIL", "title": "Entry Successful", "reachOut": "REACH OUT", "appleWatch": "Apple Watch", "phoneNumber": "PHONE NUMBER", "youCouldWin": "YOU COULD WIN..", "description1": "Great prizes await. Stay tuned for the upcoming winner announcement. ", "description2": "You can submit another raffle entry in 24 hours to increase your chances to win!", "emailAddress": "<EMAIL>", "hoursTimings": "Saturday - Thursday from 09:00 am - 09:00 pm", "workingHours": "WORKING HOURS", "downloadOurApp": "DOWNLOAD OUR APP", "winIphone16Pro": "Win iPhone 16 Pro", "phoneNumberValue": "920019339", "reachOutDescription": "We're here for you! give us a call, send an email, or fill out the form below and we’ll get back to you shortly.", "downloadAppDescription": "Your all in one gateway to ROSHN services and homes.", "socilaMediaDescription1": "Curious if you're a winner?", "socilaMediaDescription2": "Follow us on Instagram to see the official winners' announcements!"}}}, "contactUs": {"faq": {"title": "FAQs", "viewCTA": "VIEW ALL"}, "hero": {"title": "ROSHN CARE", "subtitle": "We are here for you.\nAlways ready to help."}, "faqsArgs": {"title": "Need help?", "faqsCTA": "Visit Our Faqs", "subtitle": "Check out our FAQs"}, "reachOut": {"title": "REACH OUT", "emailCTA": "<EMAIL>", "phoneCTA": "920 022 288", "subtitle": "We’re here for you! give us a call, send an email, or fill out the form below and we’ll get back to you shortly.", "emailLabel": "EMAIL", "phoneLabel": "PHONE NUMBER", "addressLabel": "ADDRESS", "workingHours": "Saturday - Thursday from 09:00 am - 09:00 pm", "workingHoursLabel": "WORKING HOURS"}, "bannerArgs": {"altText": "Image Alt Text", "cardOne": {"title": "Register Your Interest.", "caption": "Submit a request, and we will be happy to call you back."}, "cardTwo": {"title": "Visit Sales Centre.", "caption": "Book an appointment, and we will be happy to assist you."}, "contactText": "Contact US ", "contactDetailText": "Our team of experts is always available to address any questions you might have"}, "contactForm": {"title": "CONTACT FORM", "message": "Message", "submitCTA": "SUBMIT", "successMsg": "Thank you for contacting us! Our team will get back to you shortly.", "requestType": "Request type", "submitError": "An error occurred while submitting your message. Please try again.", "messageTooLong": "Message cannot exceed 500 characters", "selectPlaceholder": "Select", "messagePlaceholder": "Enter your message"}, "saleCenters": {"title": "OUR SALES CENTRES", "bookAppointment": "BOOK AN APPOINTMENT"}, "salesCenterArgs": {"title": "ROSHN Sales Centre", "subTitle": "Our sales advisors will be happy to assist you with your home journey.", "appointmentCTA": "Schedule Your Appointment", "salesCenterInfo": {"sedra": {"title": "SEDRA Sales Centre", "mapUrl": "https://www.google.com/maps/search/?api=1&query=<PERSON>+Khalid+International+Airport", "location": "King <PERSON> International Airport, Riyadh 13413, Saudi Arabia.", "markerLabel": "Roshn Sales Center - SEDRA", "workingHours": "Sun-Thu: (10 AM - 7 PM)\nSat: (1 PM - 6 PM)", "markerLinkLabel": "Open in google maps"}, "warefa": {"title": "WAREFA Sales Centre", "mapUrl": "https://www.google.com/maps/search/?api=1&query=<PERSON>+Khalid+International+Airport", "location": "Al Janadriyyah, Riyadh", "markerLabel": "مركز مبيعات روشن (مشروع وارفة", "workingHours": "Sun & Thu: (10 AM - 7 PM)\nMon, Tue & Wed: (12 PM - 9 PM)\nSat: (1 PM - 6 PM)", "markerLinkLabel": "Open in google maps"}, "alarous": {"title": "ALAROUS Sales Centre", "mapUrl": "https://www.google.com/maps/search/?api=1&query=<PERSON>+Khalid+International+Airport", "location": "Prince <PERSON> St, Al Khalidiyyah, Jeddah", "markerLabel": "Jeddah 23421,roshn", "workingHours": "Sun-Thu: (10 AM - 7 PM)\nSat: (2 PM - 6 PM)", "markerLinkLabel": "Open in google maps"}, "aldanah": {"title": "ALDANAH Sales Centre", "mapUrl": "https://www.google.com/maps/search/?api=1&query=85X6+XF, Al <PERSON>ata<PERSON>, Dhahran 34246, Saudi Arabia", "location": "Dhahran, Eastern Province", "markerLabel": "ROSHN Sales Center - ALDANAH Dhahran", "workingHours": "Sun, Mon, Tu<PERSON>, Wed & Sat: (11 AM - 8 PM)\nThu: (1 PM - 6 PM)", "markerLinkLabel": "Open in google maps"}, "almanar": {"title": "ALMANAR Sales Centre", "mapUrl": "https://www.google.com/maps/search/?api=1&query=<PERSON>+Khalid+International+Airport", "location": "Prince <PERSON> bin <PERSON>, Makkah", "markerLabel": "ROSHN Sales Center - ALMANAR makkah", "workingHours": "Sun, Mon, Tue & Thu: (10 AM - 7 PM)\nWed: (10 AM - 9 PM)\nSat: (3 PM - 7 PM)", "markerLinkLabel": "Open in google maps"}}}, "customerCareArgs": {"title": "ROSHN Customer Care", "emailCTA": "<EMAIL>", "phoneCTA": "*********", "subTitle": "Reach out to us if you have any questions and an experienced specialist will be happy to assist you.", "workingHours": "Saturday - Thursday from 09:00 am - 09:00 pm"}}, "homePagev4": {"mapArgs": {"sedra": "<PERSON><PERSON>", "title": "Our Presence", "marafy": "<PERSON><PERSON>", "warefa": "Warefa", "alarous": "Alarous", "aldanah": "Aldanah", "alfulwa": "<PERSON><PERSON><PERSON>", "almanar": "<PERSON>nar", "tagline": "Discover the latest homes for sale.", "sedraLogo": "https://alb-home.roshn.sa/SEDRA_New_V_logo_89a4ba0909/SEDRA_New_V_logo_89a4ba0909.svg", "marafyLogo": "https://alb-home.roshn.sa/MARAFY_Eng_861b5c9370/MARAFY_Eng_861b5c9370.svg", "warefaLogo": "https://alb-home.roshn.sa/WAREFA_New_Frame_23_794d6faea9/WAREFA_New_Frame_23_794d6faea9.svg", "alarousLogo": "https://alb-home.roshn.sa/ALAROUS_New_Frame_23_25f865cbad/ALAROUS_New_Frame_23_25f865cbad.svg", "aldanahLogo": "https://alb-home.roshn.sa/ALDANAH_New_Frame_23_e126649d65/ALDANAH_New_Frame_23_e126649d65.svg", "alfulwaLogo": "https://alb-home.roshn.sa/ALFULWA_Eng_f0b6fa4015/ALFULWA_Eng_f0b6fa4015.svg", "almanarLogo": "https://alb-home.roshn.sa/ALMANAR_New_Frame_23_caeda8af9a/ALMANAR_New_Frame_23_caeda8af9a.svg", "description": "ROSHN communities invite you to discover a lifestyle where culture meets modernity. ", "cardLabelSedra": "SEDRA - RIYADH", "viewMasterplan": "View Masterplan", "cardLabelMarafy": "<PERSON><PERSON>", "cardLabelWarefa": "WAREFA - RIYADH", "cardLabelALMANAR": "ALMANAR - <PERSON><PERSON><PERSON>", "cardLabelAlarous": "ALAROUS - JEDDAH", "cardLabelAldanah": "ALDANAH - Dhahran", "cardLabelAlfulwa": "ALFULWA - HAFOUF"}, "cardArgs": {"header": "Roshn Communities", "subHeader": "Experience ROSHN’s sustainable communities that elevate connectivity  — reserve your home online today.", "reserveBtn": "Reserve Home Online", "scheduleBtn": "Schedule a Visit", "sedraWhiteLogo": "https://alb-home.roshn.sa/SEDRA_New_b692cf86aa/SEDRA_New_b692cf86aa.svg", "warefaWhiteLogo": "https://alb-home.roshn.sa/WAREFA_New_f91d915b05/WAREFA_New_f91d915b05.svg", "alarousWhiteLogo": "https://alb-home.roshn.sa/ALAROUS_New_e75cbdbd80/ALAROUS_New_e75cbdbd80.svg", "aldanahWhiteLogo": "https://alb-home.roshn.sa/ALDANAH_New_dc6a3e80ee/ALDANAH_New_dc6a3e80ee.svg", "almanarWhiteLogo": "https://alb-home.roshn.sa/ALMANAR_New_670b758970/ALMANAR_New_670b758970.svg"}, "appSection": {"title": "ROSHN’S DIGITAL EXPERINCE", "heading": "Your all in one gateway to ROSHN services and homes", "description": "ROSHN App empowers homeowners and tenants to seamlessly manage their properties anytime, anywhere, right from their smartphones."}, "bannerArgs": {"altText": "Image Alt Text", "titleText": "Find and Secure Your Dream Home at ROSHN", "captionText": "Explore our communities and secure your dream home online in just a few clicks", "smallBannerOne": "Considering a new home? Visit our sales centre", "smallBannerTwo": "Have a question? Let us call you"}, "footerArgs": {"pifLogo": "https://alb-home.roshn.sa/pif_logo_en_5c9c76bcab/pif_logo_en_5c9c76bcab.svg", "faqsText": "FAQs", "copyright": "Copyright © 2024 ROSHN, All rights reserved", "aboutUsUrl": "https://www.roshn.sa/en/we-are-roshn", "aboutUsText": "About Us", "appStoreLogo": "https://alb-home.roshn.sa/App_store_320c485f1f/App_store_320c485f1f.svg", "newsAndMediaUrl": "https://www.roshn.sa/en/news-and-events", "newsAndMediaText": "News & Media", "commercialRegText": "CR No. 1010449563", "googlePlaystoreLogo": "https://alb-home.roshn.sa/Badge_fe85371719/Badge_fe85371719.svg"}, "featuresList": {"water": "Water", "garden": "Garden", "laundry": "<PERSON><PERSON><PERSON>", "project": "Project", "storage": "Storage", "elevator": "Elevator(s)", "roofDeck": "Roof Deck", "smartHome": "Smart Home", "sharedArea": "Shared area", "developedBy": "Developed by", "electricity": "Electricity", "parkingSpaces": "Parking Space", "airConditioner": "A/C unit", "matchToSaudiCode": "Match to Saudi Code", "landlineTelephone": "Landline Telephone"}, "overviewArgs": {"space": "Space", "furnishing": "Furnishing", "buildingAge": "Building Age", "availability": "Availability", "propertyType": "Property Type", "buildingFacade": "Building orientation", "singlesPurpose": "Singles purpose", "singlespurpose": "Singles purpose"}, "brokerageArgs": {"discription": "Let us help you in your journey to buy and rent your dream home.", "viewListings": "View listings", "roshnBrokerage": "Roshn Brokerage", "readyForPurchase": "Ready for Purchase/Rent"}, "multiStepForm": {"Heading": "Interested in selling or leasing your property? Select a property you wish to sell or lease.", "CardHeading": "Property Management", "CardSubHeading": "Turn your home into an investment! Submit your interest to <PERSON>Sell’ or ‘Lease’ this property below and take advantage of Roshn's property management services.", "GettingStarted": "Get started", "SellMyProperties": "Sell My Properties", "LeaseMyProperties": "Lease My Properties", "homePageTopBanner": "Unlock the potential of your property with <PERSON><PERSON><PERSON>. Ready to sell or lease? Choose your unit and connect with us today!"}, "propertyInfoArgs": {"roof": "<PERSON><PERSON>", "majlis": "<PERSON><PERSON>", "mosque": "Mosque", "balcony": "Balcony", "parking": "Parking", "storage": "Storage", "terrace": "Terrace", "backyard": "Backyard", "bedrooms": "Bedrooms", "elevator": "Elevator", "kitchens": "Kitchens", "maidRoom": "Maid Room", "bathrooms": "Bathrooms", "driverRoom": "Driver room", "powderRoom": "Powder Room", "publicPark": "Public Park", "laundryRoom": "Laundry room", "healthCentre": "Health centre", "sportsGround": "Sports ground", "diningEntertainment": "Dining & entertainment"}, "digitalExperience": {"title": "Your all in one gateway to ROSHN services and homes", "qrCode": "https://alb-home.roshn.sa/QR_9554677da5/QR_9554677da5.svg", "heading": "ROSHN’s Digital Experience", "appStore": "https://alb-home.roshn.sa/app_store_en_815f1896db/app_store_en_815f1896db.svg", "playStore": "https://alb-home.roshn.sa/google_play_en_798f2a8a25/google_play_en_798f2a8a25.svg", "appStoreUrl": "https://apps.apple.com/sa/app/roshn/id6472248406", "description": "ROSHN App empowers homeowners and tenants to seamlessly manage their properties anytime, anywhere, right from their smartphones.", "playStoreUrl": "https://play.google.com/store/apps/details?id=com.roshn.superapp&pcampaignid=web_share", "previewImage": "https://alb-home.roshn.sa/Mockups_3_b8b5323993/Mockups_3_b8b5323993.svg"}, "brokerageFilterArgs": {"any": "Any", "buy": "Buy", "gym": "Gym", "sar": "", "sqm": "sqm", "beds": "Beds", "done": "Done", "land": "Land", "pool": "Pool", "rent": "Rent", "apply": "APPLY", "baths": "Baths", "price": "Price", "reset": "Reset", "sedra": "<PERSON><PERSON>", "villa": "Villa", "where": "Where", "years": "years", "duplex": "Duplex", "garden": "Garden", "search": "Search", "select": "Select", "warefa": "Warefa", "alarous": "Alarous", "filters": "Filters", "maximum": "Maximum", "minimum": "Minimum", "parking": "Parking", "showAll": "Show all", "addRange": "Add range", "bedrooms": "Bedrooms", "cabinets": "Cabinets", "plotArea": "Plot Area", "resetAll": "Reset All", "showLess": "Show less", "apartment": "Apartment", "available": "Available", "bathrooms": "Bathrooms", "developer": "Developers", "furnished": "Furnished", "selectAll": "SELECT ALL", "townhouse": "Townhouse", "addNumbers": "Add numbers", "commercial": "Commercial", "furnishing": "Furnishing", "mustHaveAC": "Must have A/C", "priceRange": "Price Range", "communities": "Communities", "listingType": "Listing Type", "moreFilters": "More Filters", "propertyAge": "Property Age", "residential": "Residential", "selectRange": "Select range", "unfurnished": "Unfurnished", "applyFilters": "Apply Filters", "availability": "Availability", "bedsAndBaths": "Beds & Baths", "notavailable": "Not Available", "propertyType": "Property Type", "schoolNearby": "School nearby", "cityCommunity": "City, Community...", "propertyPrice": "Property Price", "semiFurnished": "Semi-Furnished", "viewLargerMap": "View larger map", "otherAmenities": "Other Amenities", "nearByAmenities": "Nearby Amenities", "nearbyAmenities": "NEARBY AMENITIES", "selectDeveloper": "Select Developers", "kitchenInstalled": "Kitchen installed", "propertyFeatures": "Property Features", "additionalDetails": "Additional Details", "filterByDeveloper": "Filter by <PERSON><PERSON><PERSON>", "roomsAndAmenities": "Rooms and amenities", "selectPropertyAge": "Select property age", "showAllProperties": "Show all properties", "showSoldProperties": "Show Sold Properties", "selectFurnishingStatus": "Select furnishing status", "selectAvailabilityStatus": "Select availability status", "onlyShowAvailableProperties": "Only show available properties"}}, "privacyPolicy": "https://www.roshn.sa/en/privacy-policy", "deliveryRobotPage": {"body": {"intro": "Nice to meet you! I may be small, but I’m packed with technology, charm, and a big mission: making your life easier, smarter, and a whole lot cooler (literally, I’ve got a built-in cooling system).", "steps": [{"stepTitle": "My First Steps: March 2024", "stepDescription": "I was born in Sedra community as part of a very special pilot project between ROSHN and Jahez. My mission? Deliver food and smiles to your doorstep—autonomously! Residents welcomed me with open arms (and hungry stomachs). I rolled through the streets, beeped happily, and proved that robots can deliver more than just meals—we can deliver joy."}, {"stepTitle": "Where I Am Now", "stepDescription": "Fast forward to today… I’ve leveled up and now live in ROSHN Business Front with some of my robot friends (there are five of us!). We zoom around offices and restaurants from 9 AM to 5 PM, helping busy people stay cool while we bring the good stuff straight to them.\n\nI’m not just a delivery bot—I’m here to set the standard for smart living:\n· Community-first\n· Eco-friendly\n· Tech-savvy\n· Always on time (well… 99.9% of the time) "}, {"stepTitle": "Why I Exist", "stepDescription": "Sure, I’m cute—but I’m also part of something bigger.\nI help test what the future of delivery can look like in Saudi Arabia. My job is to show:\n· That robots and humans can get along\n· That smart cities aren’t just ideas, they’re happening now\n· That sustainability and innovation belong together (like burger and fries)\n\nOh, and I’m also helping regulators like the TGA figure out how to bring more bots like me to your neighborhood!"}, {"stepTitle": "What Makes Me Special?", "stepDescription": "Let me brag a little. I’ve got:\n· Level 4 Autonomy – I’m super independent, but my human friends can help if I ever get lost\n· 20+ sensors and 6 cameras – nothing gets past me\n· GPS accuracy down to the centimeter – I don’t play around\n· A cute screen & voice – yeah, that’s my real face you’re looking at\n· Cooling system – because nobody wants warm ice cream\n· A modular storage belly – pizzas, shawarma, you name it"}, {"stepTitle": "Where I’m Headed", "stepDescription": "Next stop? ROSHN Front Shopping Area then on to more residential communities across the Kingdom. I’m becoming part of something much bigger, a movement to make smart living part of everyday life. \nI dream of a future where robots like me are everywhere, helping cities stay clean, green, and connected. "}], "title": "Let me tell you my story..."}, "banner": {"title": "Hi, I’m Your ROSHN x Jahez Delivery Robot!"}, "conclusion": {"title": "Share Your Robot Moment!", "description": "If I made you smile today, snap a pic and share it using #ROSHNEXT and #JahezRobotics."}, "notFoundTitle": "Oops! Page not found!"}, "articleGeneratorPage": {"showLess": "show Less", "loadMoreCta": "load more", "goBackButton": "Go back to Article list", "latestArticles": "Latest Articles", "noResultsTitle": "No search results found", "tableOfContent": {"title": "content"}, "topArticlesBtn": "Top Articles", "breadcrumbArticle": {"home": "Home", "articles": "Articles"}, "searchPlaceholder": "search a specific article", "accessibilityContent": {"Large": "Large", "Small": "Small", "Medium": "Medium", "fontSize": "font size", "ShareThisArticle": "Share This Article", "copiedToClipboard": "Copied to Clipboard"}, "noResultsDescription": "Try different keywords or check your spelling.", "articleImageDescription": " This is the description of the image"}}