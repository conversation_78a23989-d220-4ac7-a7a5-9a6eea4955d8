import { create } from "zustand";
import { persist } from "zustand/middleware";

interface AuthState {
  user: string | null;
  isAuthenticated: boolean;
  login: (token: string) => void;
  logout: () => void;
}

/*
This is a sample store created to show how to implement 
stores with zustand.

*/
export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null as string | null,
      isAuthenticated: false,
      login: (token: string) => set({ isAuthenticated: true, user: token }),
      logout: () => set({ isAuthenticated: false, user: null }),
    }),
    {
      name: "auth-storage",
    },
  ),
);
