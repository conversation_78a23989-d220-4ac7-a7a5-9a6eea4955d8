import { describe, it, expect } from "vitest";

import { useAuthStore } from "./useAuthStore";

describe("useAuthStore", () => {
  it("should have initial state", () => {
    const state = useAuthStore.getState();
    expect(state.user).toBeNull();
    expect(state.isAuthenticated).toBe(false);
  });

  it("should update state on login", () => {
    const token = "test-token";
    useAuthStore.getState().login(token);
    const state = useAuthStore.getState();
    expect(state.user).toBe(token);
    expect(state.isAuthenticated).toBe(true);
  });

  it("should clear state on logout", () => {
    useAuthStore.getState().logout();
    const state = useAuthStore.getState();
    expect(state.user).toBeNull();
    expect(state.isAuthenticated).toBe(false);
  });
});
