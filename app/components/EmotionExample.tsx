/** @jsxImportSource @emotion/react */
import { css } from "@emotion/react";

import { DirectionToggle } from "./DirectionToggle";

export function EmotionExample() {
  const isDark = false; // Simplified for now
  const isRTL = false; // Simplified for now

  const containerStyle = css`
    padding: 2rem;
    max-width: 42rem;
    margin: 0 auto;
    background-color: white;
    color: #333;
    min-height: 100vh;

    & > * + * {
      margin-top: 2rem;
    }
  `;

  const headerStyle = css`
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
  `;

  const titleStyle = css`
    font-size: 1.5rem;
    font-weight: bold;
    color: #0066ff;
    margin: 0;
  `;

  const cardStyle = css`
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  `;

  const statusStyle = css`
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    font-size: 0.875rem;

    strong {
      color: #0066ff;
    }
  `;

  const buttonGroupStyle = css`
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  `;

  const demoButtonStyle = css`
    padding: 0.5rem 1rem;
    background-color: #0066ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;

    &:hover {
      background-color: #0052cc;
    }
  `;

  const secondaryButtonStyle = css`
    padding: 0.5rem 1rem;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.875rem;

    &:hover {
      background-color: #5a6268;
    }
  `;

  const outlineButtonStyle = css`
    padding: 0.375rem 0.75rem;
    background-color: transparent;
    color: #0066ff;
    border: 1px solid #0066ff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;

    &:hover {
      background-color: #0066ff;
      color: white;
    }
  `;

  return (
    <div css={containerStyle}>
      <header css={headerStyle}>
        <h1 css={titleStyle}>Emotion CSS + RDS Theme</h1>
        <div css={buttonGroupStyle}>
          <DirectionToggle />
        </div>
      </header>

      <div css={cardStyle}>
        <h2
          css={css`
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0 0 0.75rem 0;
            color: #495057;
          `}
        >
          Theme Status
        </h2>
        <div css={statusStyle}>
          <div>
            Theme Mode: <strong>{isDark ? "Dark" : "Light"}</strong>
          </div>
          <div>
            Direction: <strong>{isRTL ? "RTL" : "LTR"}</strong>
          </div>
        </div>
      </div>

      <div css={cardStyle}>
        <h2
          css={css`
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
            color: #495057;
          `}
        >
          Button Examples
        </h2>
        <div css={buttonGroupStyle}>
          <button css={demoButtonStyle}>Primary Button</button>
          <button css={secondaryButtonStyle}>Secondary Button</button>
          <button css={outlineButtonStyle}>Outline Button</button>
        </div>
      </div>

      <div css={cardStyle}>
        <h2
          css={css`
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
            color: #495057;
          `}
        >
          Color Palette
        </h2>
        <div
          css={css`
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.75rem;
          `}
        >
          {(["primary", "secondary", "success", "warning", "error"] as const).map((colorName) => (
            <div
              key={colorName}
              css={css`
                text-align: center;
              `}
            >
              <div
                css={css`
                  width: 100%;
                  height: 60px;
                  background-color: ${colorName === "primary"
                    ? "#0066ff"
                    : colorName === "secondary"
                      ? "#6c757d"
                      : colorName === "success"
                        ? "#28a745"
                        : colorName === "warning"
                          ? "#ffc107"
                          : "#dc3545"};
                  border-radius: 4px;
                  margin-bottom: 0.5rem;
                  border: 1px solid #e9ecef;
                `}
              />
              <div
                css={css`
                  font-size: 0.75rem;
                  font-weight: 500;
                  color: #495057;
                  text-transform: capitalize;
                `}
              >
                {colorName}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
