/**
 * By default, <PERSON> will handle hydrating your app on the client for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.client
 */
import "reflect-metadata/lite";
import { RemixBrowser } from "@remix-run/react";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";
import { I18nextProvider } from "react-i18next";

import { getNonceOnClient, NonceProvider } from "~/context/csp-provider";

import { bindImplsClient } from "./di/bind-impls.client";
import { appContainer } from "./di/create-client-container";
import { DIProvider } from "./hooks/use-di";
import { setupI18nClient, i18n } from "./i18n/i18n.client";
import { initLogger } from "./lib/logger/init-logger";
import { getLangFromUrl } from "./utils/get-lang";

if (import.meta.env.MODE === "development" && import.meta.env.VITE_ENABLE_MSW) {
  import("./mocks/browser").then(({ worker }) => {
    worker.start();
  });
}

bindImplsClient(appContainer);
initLogger(appContainer);
const nonce = getNonceOnClient();

const hydrate = async () => {
  const lng = getLangFromUrl(location.href);
  await setupI18nClient(i18n, lng, appContainer);

  startTransition(() => {
    hydrateRoot(
      document,
      <NonceProvider nonce={nonce}>
        <I18nextProvider i18n={i18n}>
          <DIProvider container={appContainer}>
            <StrictMode>
              <RemixBrowser />
            </StrictMode>
          </DIProvider>
        </I18nextProvider>
      </NonceProvider>,
    );
  });
};

hydrate();
