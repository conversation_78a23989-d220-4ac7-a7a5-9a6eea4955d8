/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, {
  Axios,
  InternalAxiosRequestConfig,
  AxiosInstance,
  AxiosResponse,
  AxiosError,
  HttpStatusCode,
} from "axios";
import { injectable } from "inversify";
import mitt from "mitt";

// import { ForgeRockBridge } from "../forgerock-bridge";

import {
  Events,
  HttpClient,
  HttpClientFactory,
  HttpRequestConfig,
  HttpResponse,
} from "./http-client-factory";

export class HttpClientImpl implements HttpClient {
  constructor(readonly axios: Axios) {}

  request<T = any, R = HttpResponse<T, any>, D = any>(config: HttpRequestConfig<D>): Promise<R> {
    return this.axios.request(config);
  }
  get<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.get(url, config);
  }
  delete<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.delete(url, config);
  }
  head<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.head(url, config);
  }
  options<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.options(url, config);
  }
  post<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    data?: D | undefined,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.post(url, data, config);
  }
  put<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    data?: D | undefined,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.put(url, data, config);
  }
  patch<T = any, R = HttpResponse<T, any>, D = any>(
    url: string,
    data?: D | undefined,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<R> {
    return this.axios.patch(url, data, config);
  }
}

export type TokenGenerator = () => Promise<string | undefined>;

@injectable()
export class HttpClientFactoryImpl implements HttpClientFactory {
  static create(config?: HttpRequestConfig<any> | undefined) {
    const create =
      typeof axios.create === "function" ? axios.create : (axios as any).default.create;

    return create({
      ...config,
    }) as AxiosInstance;
  }

  private readonly mitt = mitt<Events>();

  constructor() {} // private readonly forgeRockBridge: ForgeRockBridge // @inject<ForgeRockBridge>(ForgeRockBridge)

  private readonly emitUnauthenticated = () => {
    this.mitt.emit("unauthenticated");
  };

  private readonly tokenInjector = async (
    config: InternalAxiosRequestConfig,
  ): Promise<InternalAxiosRequestConfig> => {
    // const theConfig = config as HttpRequestConfig<any>;

    // const oauth2token = await this.forgeRockBridge.getTokens();

    // if (!oauth2token) {
    //   this.emitUnauthenticated();
    //   throw new Error("No token found");
    // }

    // theConfig.headers = {
    //   ...config.headers,
    //   Authorization: `Bearer ${oauth2token.accessToken}`,
    // };

    return config as InternalAxiosRequestConfig;
  };

  private readonly unauthenticatedInterceptor = (error: AxiosError) => {
    const response = error.response as AxiosResponse;
    if (response?.status === HttpStatusCode.Unauthorized) {
      this.emitUnauthenticated();
    }
    return response;
  };

  on: <E extends "unauthenticated">(event: E, listener: (event: Events[E]) => void) => this = (
    event,
    listener,
  ) => {
    this.mitt.on(event, listener);
    return this;
  };

  off: <E extends "unauthenticated">(event: E, listener: (event: Events[E]) => void) => this = (
    event,
    listener,
  ) => {
    this.mitt.off(event, listener);

    return this;
  };

  create(config?: HttpRequestConfig<any> | undefined): HttpClient {
    const instance = HttpClientFactoryImpl.create({
      ...config,
    });

    instance.interceptors.request.use(this.tokenInjector);

    instance.interceptors.response.use(undefined, this.unauthenticatedInterceptor);

    return new HttpClientImpl(instance);
  }
}

/**
 * Does not depend on ForgeRockBridge, for reducing testing boilerplate.
 */
@injectable()
export class HttpClientFactoryTestImpl implements HttpClientFactory {
  private readonly mitt = mitt<Events>();

  on: <E extends "unauthenticated">(event: E, listener: (event: Events[E]) => void) => this = (
    event,
    listener,
  ) => {
    this.mitt.on(event, listener);
    return this;
  };

  off: <E extends "unauthenticated">(event: E, listener: (event: Events[E]) => void) => this = (
    event,
    listener,
  ) => {
    this.mitt.off(event, listener);

    return this;
  };

  create(config?: HttpRequestConfig<any> | undefined): HttpClient {
    const instance = HttpClientFactoryImpl.create({
      ...config,
    });

    return new HttpClientImpl(instance);
  }
}
