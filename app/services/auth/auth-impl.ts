import { injectable } from "inversify";

import { <PERSON>r<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IAuthService } from "~/interface/authTypes";

@injectable()
export class AuthService implements IAuthService {
  constructor() {
    // You can perform any necessary setup or initialization here
  }
  async authenticateUser(_userAuth: UserAuth): Promise<JWTToken> {
    // Implement your authentication logic here
    // For example, you might make a request to your backend API to authenticate the user
    // and receive a JWT token in return
    // For now, let's just return a dummy token
    return {
      token: "dummy-token",
      expiresIn: 3600,
    };
  }
}
