import { CommonEnvService, Mode } from "./env";

/**
 * Common environment variables implementation
 * This contains variables that are available in both client and server contexts
 */
export const commonEnvServiceImpl: CommonEnvService = {
  MODE: import.meta.env.MODE as Mode,
  STRAPI_TOKEN: import.meta.env.VITE_STRAPI_TOKEN || "",
  STRAPI_URL: import.meta.env.VITE_STRAPI_URL || "",
  APP_API_URL: import.meta.env.VITE_APP_API_URL || "",
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || "info",
  GOOGLE_TAG_MANAGER_ID: import.meta.env.VITE_GOOGLE_TAG_MANAGER_ID || "",
};
