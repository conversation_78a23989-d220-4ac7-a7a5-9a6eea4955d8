export type Mode = "dev" | "production" | "uat";

/**
 * Common environment variables available in both client and server contexts
 */
export interface CommonEnvService {
  readonly MODE: Mode;
  readonly STRAPI_URL: string;
  readonly STRAPI_TOKEN: string;
  readonly APP_API_URL: string;
  readonly LOG_LEVEL: "debug" | "info" | "warn" | "error";
  readonly GOOGLE_TAG_MANAGER_ID: string;
}

/**
 * To be used by services that can be used on client only.
 * By default used in client container
 */
export interface ClientEnvService extends CommonEnvService {}

/**
 * To be used by services that can be used on server only.
 * By default used in server container
 */
export interface ServerEnvService extends CommonEnvService {
  // CSP (Content Security Policy) variables
  readonly CSP_UPGRADE_INSECURE_REQUESTS: string;
  readonly CSP_DEFAULT_SRC: string;
  readonly CSP_SCRIPT_SRC: string;
  readonly CSP_BASE_URI: string;
  readonly CSP_FRAME_ANCESTORS: string;
  readonly CSP_FRAME_SRC: string;
  readonly CSP_OBJECT_SRC: string;
  readonly CSP_FORM_ACTION: string;
  readonly CSP_IMG_SRC: string;
  readonly CSP_STYLE_SRC: string;
  readonly CSP_CONNECT_SRC: string;
  readonly CSP_FONT_SRC: string;
  readonly CSP_SCRIPT_SRC_ATTR: string;
  readonly CSP_SCRIPT_SRC_ELEM: string;
  readonly CSP_SCRIPT_SRC_NONCE: string;
  readonly CSP_SCRIPT_SRC_NONCE_ATTR: string;
  readonly CSP_SCRIPT_SRC_NONCE_ELEM: string;
  // HSTS (HTTP Strict Transport Security) variables
  readonly HSTS_MAX_AGE: string;
  readonly HSTS_INCLUDE_SUBDOMAINS: string;
  readonly HSTS_PRELOAD: string;
  // Other security headers
  readonly REFERRER_POLICY: string;
  readonly CROSS_ORIGIN_RESOURCE_POLICY: string;
  readonly X_CONTENT_TYPE_OPTIONS: string;
  readonly X_DNS_PREFETCH_CONTROL: string;
  readonly X_XSS_PROTECTION: string;
  readonly X_FRAME_OPTIONS: string;
}

// To be used by services that can be used on both client and server
export interface EnvService extends CommonEnvService {}

export const EnvService = Symbol("EnvService");
