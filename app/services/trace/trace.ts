export interface TraceService {
  /**
   * Gets the current session's trace ID.
   */
  getTraceId(): string;

  /**
   * Gets the current span ID.
   */
  getSpanId(): string;

  /**
   * Generates and sets a new span ID, returning the newly created value.
   */
  generateNewSpanId(): string;

  /**
   * Generates the W3C Trace Context `traceparent` header value.
   * Format: 00-{traceId}-{spanId}-01
   */
  getTraceParent(): string;
}

export const TraceService = Symbol("TraceService");
