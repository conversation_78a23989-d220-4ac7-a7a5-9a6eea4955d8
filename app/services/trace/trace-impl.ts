import { TraceService } from "./trace";

export class TraceServiceImpl implements TraceService {
  private static instance: TraceServiceImpl;
  private traceId: string;
  private spanId: string;

  private constructor() {
    this.traceId = this.generateId(16); // 16 bytes = 32 hex characters
    this.spanId = this.generateId(8); // 8 bytes = 16 hex characters
  }

  public static getInstance(): TraceService {
    if (!TraceServiceImpl.instance) {
      TraceServiceImpl.instance = new TraceServiceImpl();
    }
    return TraceServiceImpl.instance;
  }

  private generateId(byteLength: number): string {
    const randomBytes = new Uint8Array(byteLength);
    // This is safe to use in both modern browsers and server-side Node.js environments.
    crypto.getRandomValues(randomBytes);
    return Array.from(randomBytes)
      .map((b) => b.toString(16).padStart(2, "0"))
      .join("");
  }

  public getTraceId(): string {
    return this.traceId;
  }

  public getSpanId(): string {
    return this.spanId;
  }

  public generateNewSpanId(): string {
    this.spanId = this.generateId(8);
    return this.spanId;
  }

  public getTraceParent(): string {
    // Version '00', sampled flag '01'
    return `00-${this.traceId}-${this.spanId}-01`;
  }
}
