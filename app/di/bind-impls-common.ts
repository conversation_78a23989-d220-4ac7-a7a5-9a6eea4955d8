import { Container } from "inversify";

import { HttpClientFactory, HttpClientFactoryImpl } from "~/services/http-client-factory";
import { TranslationService, TranslationServiceImpl } from "~/services/translation";

import { StrapiImpl, StrapiService } from "../services/strapi";

export function bindImplsCommon(container: Container) {
  // Bind services
  container.bind<HttpClientFactory>(HttpClientFactory).to(HttpClientFactoryImpl);
  container.bind<StrapiService>(StrapiService).to(StrapiImpl);
  container.bind<TranslationService>(TranslationService).to(TranslationServiceImpl);
}
