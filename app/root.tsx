import "reflect-metadata";
import createEmotionCache from "@emotion/cache";
import type { LinksFunction, LoaderFunction } from "@remix-run/node";
import {
  Links,
  Meta,
  MetaFunction,
  Outlet,
  redirect,
  Scripts,
  ScrollRestoration,
} from "@remix-run/react";
import { useState } from "react";

import "./styles/global.css";
import "./styles/language-switcher.css";

import { LanguageSwitcher } from "~/components/language-switcher/language-switcher";
import { AuthProvider } from "~/context/authContext";
import { CoreThemeProvider } from "~/context/coreProvider";
import { NonceScript, useNonce } from "~/context/csp-provider";
import { ReactQueryProvider } from "~/context/reactQueryProvider";
import { useLocalization } from "~/hooks/use-localization";
import { Analytics } from "~/lib/analytics";
import { langRedirectHandler } from "~/utils/lang-redirect-handler";
import { isServer } from "~/utils/shared";

export const meta: MetaFunction = ({ matches }) => {
  const parentMeta = matches
    .flatMap((match) => match.meta ?? [])
    .filter((meta) => !("title" in meta));
  return [
    ...parentMeta,
    {
      name: "viewport",
      content: "width=device-width,initial-scale=1",
    },
    { title: "New Remix App" },
  ];
};

export const links: LinksFunction = () => {
  const preconnectLinks = import.meta.env.VITE_STRAPI_URL
    ? [
        {
          rel: "preconnect",
          href: import.meta.env.VITE_STRAPI_URL,
        },
      ]
    : [];
  return [...preconnectLinks];
};

// Extended theme configuration with direction support
const themeConfig = {
  mode: "light" as const,
  direction: "ltr" as const,
  name: "light" as const,
};

export const loader: LoaderFunction = async ({ request, params }) => {
  const { pathname, search } = new URL(request.url);

  // Redirect to the root path if the pathname ends with a slash
  if (pathname.endsWith("/") && pathname !== "/") {
    return redirect(`${pathname.replace(/\/+$/, "") || "/"}${search}`, {
      status: 308,
    });
  }

  const { getRedirectUri, performRedirect, shouldRedirect } = langRedirectHandler({
    params,
    request,
  });

  if (shouldRedirect()) {
    return performRedirect(getRedirectUri());
  }

  return null;
};

export function Layout({ children }: { children: React.ReactNode }) {
  const [cache] = useState(() =>
    createEmotionCache({
      key: "roshn-boilerplate-fe",
    }),
  );
  const { currentLanguage, currentDirection } = useLocalization();
  const { nonce } = useNonce();

  return (
    <html lang={currentLanguage} dir={currentDirection}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
        {isServer && (
          <>
            <NonceScript />
            <Analytics />
          </>
        )}
      </head>
      <body data-theme={themeConfig.mode} data-direction={currentDirection}>
        <AuthProvider>
          <ReactQueryProvider>
            <CoreThemeProvider cache={cache}>
              <div className="app-header">
                <LanguageSwitcher />
              </div>
              {children}
            </CoreThemeProvider>
          </ReactQueryProvider>
        </AuthProvider>
        <ScrollRestoration nonce={nonce} />
        <Scripts nonce={nonce} />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}
