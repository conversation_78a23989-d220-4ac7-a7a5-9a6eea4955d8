import { MetaFunction } from "@remix-run/react";
import { ClientOnly } from "remix-utils/client-only";

import NotFound from "~/pages/not-found/not-found";

export const meta: MetaFunction = () => {
  return [{ title: "Not Found" }];
};

export const loader = () => {
  return new Response(null, { status: 404 });
};

export default function NotFoundRoute() {
  return <ClientOnly>{() => <NotFound />}</ClientOnly>;
}
