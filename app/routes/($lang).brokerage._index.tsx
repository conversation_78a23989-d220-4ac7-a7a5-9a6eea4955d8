import type { MetaFunction } from "@remix-run/node";

import LandingPage from "~/pages/homepage/homepage";

export const meta: MetaFunction = () => {
  return [
    { title: "Roshn Boilerplate FE - RDS Theme" },
    {
      name: "description",
      content: "A modern, responsive web application boilerplate built with RDS Theme System",
    },
  ];
};

export default function Index() {
  return <LandingPage />;
}
