import { describe, it, expect } from "vitest";

import { langRedirectHandler } from "../lang-redirect-handler";

/**
 * Creates a mock Request object for testing.
 * @param path - The URL path for the request.
 * @param headers - Optional request headers.
 * @returns A mock Request object.
 */
const createMockRequest = (path: string, headers?: HeadersInit): Request => {
  return new Request(`https://example.com${path}`, { headers });
};

describe("langRedirectHandler", () => {
  describe("shouldRedirect", () => {
    it("should return true if lang param is missing", () => {
      const request = createMockRequest("/some/path");
      const { shouldRedirect } = langRedirectHandler({ request, params: {} });
      expect(shouldRedirect()).toBe(true);
    });

    it("should return true if lang param is not supported", () => {
      const request = createMockRequest("/es/some/path");
      const { shouldRedirect } = langRedirectHandler({ request, params: { lang: "es" } });
      expect(shouldRedirect()).toBe(true);
    });

    it("should return false if lang param is supported", () => {
      const request = createMockRequest("/en/some/path");
      const { shouldRedirect } = langRedirectHandler({ request, params: { lang: "en" } });
      expect(shouldRedirect()).toBe(false);
    });
  });

  describe("getRedirectUri", () => {
    it("should redirect to the preferred language from headers", () => {
      const request = createMockRequest("/some/path", { "accept-language": "ar-SA,ar;q=0.9" });
      const { getRedirectUri } = langRedirectHandler({ request, params: {} });
      expect(getRedirectUri()).toBe("/ar/brokerage/some/path");
    });

    it("should fall back to default language if header language is not supported", () => {
      const request = createMockRequest("/some/path", { "accept-language": "fr-FR,fr;q=0.9" });
      const { getRedirectUri } = langRedirectHandler({ request, params: {} });
      expect(getRedirectUri()).toBe("/en/brokerage/some/path");
    });

    it("should fall back to default language if header is not present", () => {
      const request = createMockRequest("/some/path");
      const { getRedirectUri } = langRedirectHandler({ request, params: {} });
      expect(getRedirectUri()).toBe("/en/brokerage/some/path");
    });

    it("should preserve the original pathname and search params", () => {
      const request = createMockRequest("/some/path?foo=bar&baz=qux", { "accept-language": "ar" });
      const { getRedirectUri } = langRedirectHandler({ request, params: {} });
      expect(getRedirectUri()).toBe("/ar/brokerage/some/path?foo=bar&baz=qux");
    });
  });
});
