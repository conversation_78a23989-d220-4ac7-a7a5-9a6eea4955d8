import { ReactNode } from "react";

import GuestAppBar from "~/components/app-bar/guest-app-bar";

interface PublicLayoutProps {
  children: ReactNode;
}

export default function PublicLayout({ children }: PublicLayoutProps) {
  return (
    <div>
      <GuestAppBar />
      <h3>Public Layout</h3>
      <aside>Sidebar</aside>
      <main>{children}</main>
      <footer>Footer</footer>
    </div>
  );
}
