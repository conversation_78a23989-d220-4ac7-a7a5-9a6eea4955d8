/// <reference types="vite/client" />

interface Window {
  __remixNonce: string;
}

interface ImportMetaEnv {
  readonly VITE_ENV: "LOCAL" | "DEV" | "UAT" | "PROD";
  readonly VITE_STRAPI_URL: string;
  readonly VITE_STRAPI_TOKEN: string;
  readonly VITE_ENABLE_MSW: string;
  readonly VITE_SSR_STATIC_PATH: string;
  readonly VITE_APP_BASE_URI: string;
  readonly VITE_LOG_LEVEL: "debug" | "info" | "warn" | "error";

  readonly VITE_GOOGLE_TAG_MANAGER_ID: string;

  // CSP (Content Security Policy) variables
  readonly VITE_CSP_UPGRADE_INSECURE_REQUESTS: string;
  readonly VITE_CSP_DEFAULT_SRC: string;
  readonly VITE_CSP_SCRIPT_SRC: string;
  readonly VITE_CSP_BASE_URI: string;
  readonly VITE_CSP_FRAME_ANCESTORS: string;
  readonly VITE_CSP_FRAME_SRC: string;
  readonly VITE_CSP_OBJECT_SRC: string;
  readonly VITE_CSP_FORM_ACTION: string;
  readonly VITE_CSP_IMG_SRC: string;
  readonly VITE_CSP_STYLE_SRC: string;
  readonly VITE_CSP_CONNECT_SRC: string;
  readonly VITE_CSP_FONT_SRC: string;
  readonly VITE_CSP_SCRIPT_SRC_ATTR: string;
  readonly VITE_CSP_SCRIPT_SRC_ELEM: string;
  readonly VITE_CSP_SCRIPT_SRC_NONCE: string;
  readonly VITE_CSP_SCRIPT_SRC_NONCE_ATTR: string;
  readonly VITE_CSP_SCRIPT_SRC_NONCE_ELEM: string;

  // HSTS (HTTP Strict Transport Security) variables
  readonly VITE_HSTS_MAX_AGE: string;
  readonly VITE_HSTS_INCLUDE_SUBDOMAINS: string;
  readonly VITE_HSTS_PRELOAD: string;

  // Other security headers
  readonly VITE_REFERRER_POLICY: string;
  readonly VITE_CROSS_ORIGIN_RESOURCE_POLICY: string;
  readonly VITE_X_CONTENT_TYPE_OPTIONS: string;
  readonly VITE_X_DNS_PREFETCH_CONTROL: string;
  readonly VITE_X_XSS_PROTECTION: string;
  readonly VITE_X_FRAME_OPTIONS: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
