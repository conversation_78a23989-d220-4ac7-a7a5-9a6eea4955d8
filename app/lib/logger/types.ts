export type LogLevel = "debug" | "info" | "warn" | "error";

export type Severity = "CRITICAL" | "ERROR" | "WARNING" | "INFO" | "DEBUG";

export type SYSTEM = string;

export interface LogParams {
  message: string;
  system?: SYSTEM;
  metadata?: Record<string, any>;
}

export interface ErrorLogParams extends LogParams {
  error?: Error;
}

export interface StructuredLogData {
  amplitudeId: string | undefined;
  clarityId?: string | null;
  clientNamespace?: string;
  errorName?: string;
  lastApiCall?: string;
  level?: LogLevel;
  message: string;
  metadata?: Record<string, any>;
  module?: string;
  page?: string;
  severity: Severity;
  source?: string;
  spanId?: string;
  stackTrace?: string;
  system?: SYSTEM;
  timestamp: string;
  traceId: string;
}

export type LogEntry = StructuredLogData;
