import { TraceServiceImpl } from "~/services/trace";

import { Transport } from "./transport";
import { LogEntry, LogLevel, Severity, LogParams, ErrorLogParams } from "./types";

export class Logger {
  private static instance: Logger;
  private transports: Transport[] = [];
  private readonly logLevels: LogLevel[] = ["debug", "info", "warn", "error"];
  private currentLevel: LogLevel = "info";
  private amplitudeId?: string;
  private clarityId?: string | null;
  private clientNamespace?: string;

  private constructor() {}

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public setLogLevel(level: LogLevel) {
    this.currentLevel = level;
  }

  public setAmplitudeId(id: string) {
    this.amplitudeId = id;
  }

  public setClarityId(id: string | null) {
    this.clarityId = id;
  }

  public setClientNamespace(namespace: string) {
    this.clientNamespace = namespace;
  }

  public addTransport(transport: Transport): void {
    this.transports.push(transport);
  }

  private isSeverityAllowed(level: LogLevel, transportLevel?: LogLevel): boolean {
    if (!transportLevel) {
      return true;
    }
    return this.logLevels.indexOf(level) >= this.logLevels.indexOf(transportLevel);
  }

  private mapLogLevelToSeverity(level: LogLevel): Severity {
    switch (level) {
      case "error":
        return "ERROR";
      case "warn":
        return "WARNING";
      case "info":
        return "INFO";
      case "debug":
        return "DEBUG";
      default:
        return "INFO";
    }
  }

  private log(level: LogLevel, params: LogParams) {
    if (!this.isSeverityAllowed(level, this.currentLevel)) {
      return;
    }

    const traceService = TraceServiceImpl.getInstance();
    const severity = this.mapLogLevelToSeverity(level);

    const entry: LogEntry = {
      amplitudeId: this.amplitudeId,
      clarityId: this.clarityId,
      clientNamespace: this.clientNamespace,
      level,
      message: params.message,
      metadata: params.metadata,
      severity,
      system: params.system || "FRONTEND",
      timestamp: new Date().toISOString(),
      traceId: traceService.getTraceId(),
      spanId: traceService.getSpanId(),
    };

    this.transports.forEach((transport) => {
      if (this.isSeverityAllowed(level, transport.level)) {
        transport.log(entry).catch((error) => {
          console.error("Logger transport failed:", error);
        });
      }
    });
  }

  public debug(params: LogParams) {
    this.log("debug", params);
  }

  public info(params: LogParams) {
    this.log("info", params);
  }

  public warn(params: LogParams) {
    this.log("warn", params);
  }

  public error(params: ErrorLogParams) {
    const entry: LogParams = {
      ...params,
      metadata: {
        ...params.metadata,
        errorName: params.error?.name,
        stackTrace: params.error?.stack,
      },
    };
    this.log("error", entry);
  }
}

export const logger = Logger.getInstance();
