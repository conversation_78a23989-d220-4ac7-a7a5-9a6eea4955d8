import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

import { ConsoleTransport } from "../transports/console.transport";
import { LogEntry } from "../types";

describe("ConsoleTransport", () => {
  let transport: ConsoleTransport;
  let consoleSpy: {
    log: ReturnType<typeof vi.spyOn>;
    debug: ReturnType<typeof vi.spyOn>;
    warn: ReturnType<typeof vi.spyOn>;
    error: ReturnType<typeof vi.spyOn>;
    groupCollapsed: ReturnType<typeof vi.spyOn>;
    groupEnd: ReturnType<typeof vi.spyOn>;
    trace: ReturnType<typeof vi.spyOn>;
  };

  beforeEach(() => {
    transport = new ConsoleTransport();
    // Reset all mocks
    vi.restoreAllMocks();

    consoleSpy = {
      log: vi.spyOn(console, "log"),
      debug: vi.spyOn(console, "debug"),
      warn: vi.spyOn(console, "warn"),
      error: vi.spyOn(console, "error"),
      groupCollapsed: vi.spyOn(console, "groupCollapsed"),
      groupEnd: vi.spyOn(console, "groupEnd"),
      trace: vi.spyOn(console, "trace"),
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe("log", () => {
    it.each([
      ["debug", "#6c757d"],
      ["info", "#0dcaf0"],
      ["warn", "#ffc107"],
      ["error", "#dc3545"],
    ])("should format %s level with correct color", async (level, color) => {
      const entry: LogEntry = {
        level: level as LogEntry["level"],
        message: "test message",
        severity: "INFO",
        timestamp: new Date().toISOString(),
        system: "FRONTEND",
        metadata: { test: "data" },
        amplitudeId: "test-amp",
        clarityId: "test-clarity",
        clientNamespace: "test-namespace",
        traceId: "test-trace",
        spanId: "test-span",
      };

      await transport.log(entry);

      expect(consoleSpy.groupCollapsed).toHaveBeenCalledWith("%ctest message", `color: ${color};`);

      const method = level === "info" ? "log" : level;
      expect(consoleSpy[method as keyof typeof consoleSpy]).toHaveBeenCalled();
      expect(consoleSpy.trace).toHaveBeenCalledWith("Stack trace");
      expect(consoleSpy.groupEnd).toHaveBeenCalled();
    });

    it("should format log entry metadata", async () => {
      const entry: LogEntry = {
        level: "info",
        message: "test message",
        severity: "INFO",
        timestamp: "2024-01-01T00:00:00.000Z", // Fixed timestamp for consistent testing
        system: "FRONTEND",
        metadata: { test: "data" },
        traceId: "trace-123",
        spanId: "span-456",
        amplitudeId: "test-amp",
        clarityId: "test-clarity",
        clientNamespace: "test-namespace",
      };

      await transport.log(entry);

      const expectedLog = JSON.stringify(
        {
          level: "info",
          severity: "INFO",
          timestamp: entry.timestamp,
          system: "FRONTEND",
          metadata: { test: "data" },
          traceId: "trace-123",
          spanId: "span-456",
          amplitudeId: "test-amp",
          clarityId: "test-clarity",
          clientNamespace: "test-namespace",
        },
        null,
        2,
      );

      expect(consoleSpy.log).toHaveBeenCalledWith(expectedLog);
    });

    it("should not log if level is not provided", async () => {
      const entry: LogEntry = {
        message: "test message",
        severity: "INFO",
        timestamp: new Date().toISOString(),
        system: "FRONTEND",
        metadata: {},
        amplitudeId: "test-amp",
        clarityId: "test-clarity",
        clientNamespace: "test-namespace",
        traceId: "test-trace",
        spanId: "test-span",
      };

      await transport.log(entry);

      expect(consoleSpy.groupCollapsed).not.toHaveBeenCalled();
      expect(consoleSpy.log).not.toHaveBeenCalled();
      expect(consoleSpy.trace).not.toHaveBeenCalled();
      expect(consoleSpy.groupEnd).not.toHaveBeenCalled();
    });
  });
});
