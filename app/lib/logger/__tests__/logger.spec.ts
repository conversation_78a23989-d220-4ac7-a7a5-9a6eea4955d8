import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

import { Logger } from "../logger";
import { Transport } from "../transport";
import { LogEntry, LogLevel } from "../types";

// Mock TraceService
vi.mock("~/services/trace", () => ({
  TraceServiceImpl: {
    getInstance: vi.fn(() => ({
      getTraceId: () => "mock-trace-id",
      getSpanId: () => "mock-span-id",
    })),
  },
}));

class MockTransport implements Transport {
  public level?: LogLevel;
  public logs: LogEntry[] = [];

  async log(entry: LogEntry): Promise<void> {
    this.logs.push(entry);
  }
}

describe("Logger", () => {
  let logger: Logger;
  let mockTransport: MockTransport;

  beforeEach(() => {
    logger = Logger.getInstance();
    mockTransport = new MockTransport();
    logger.addTransport(mockTransport);
  });

  afterEach(() => {
    vi.clearAllMocks();
    // Reset logger instance
    (logger as any).transports = [];
    (logger as any).currentLevel = "info";
    (logger as any).amplitudeId = undefined;
    (logger as any).clarityId = undefined;
    (logger as any).clientNamespace = undefined;
  });

  describe("getInstance", () => {
    it("should return the same instance", () => {
      const instance1 = Logger.getInstance();
      const instance2 = Logger.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe("setLogLevel", () => {
    it("should set the log level", () => {
      logger.setLogLevel("debug");
      logger.debug({ message: "test", metadata: {} });
      expect(mockTransport.logs).toHaveLength(1);

      logger.setLogLevel("error");
      logger.debug({ message: "test", metadata: {} });
      expect(mockTransport.logs).toHaveLength(1); // No new log added
    });
  });

  describe("setAmplitudeId", () => {
    it("should set amplitude ID", () => {
      const amplitudeId = "test-amp-id";
      logger.setAmplitudeId(amplitudeId);
      logger.info({ message: "test", metadata: {} });
      expect(mockTransport.logs[0].amplitudeId).toBe(amplitudeId);
    });
  });

  describe("setClarityId", () => {
    it("should set clarity ID", () => {
      const clarityId = "test-clarity-id";
      logger.setClarityId(clarityId);
      logger.info({ message: "test", metadata: {} });
      expect(mockTransport.logs[0].clarityId).toBe(clarityId);
    });
  });

  describe("setClientNamespace", () => {
    it("should set client namespace", () => {
      const namespace = "test-namespace";
      logger.setClientNamespace(namespace);
      logger.info({ message: "test", metadata: {} });
      expect(mockTransport.logs[0].clientNamespace).toBe(namespace);
    });
  });

  describe("log methods", () => {
    it.each([
      ["debug", "DEBUG"],
      ["info", "INFO"],
      ["warn", "WARNING"],
      ["error", "ERROR"],
    ])("should log %s level correctly", async (level, severity) => {
      // Set log level to ensure the message will be logged
      logger.setLogLevel(level as LogLevel);

      const message = "test message";
      const metadata = { test: "data" };

      await (logger as any)[level]({ message, metadata });

      const log = mockTransport.logs[0];
      expect(log).toMatchObject({
        level,
        severity,
        message,
        metadata,
        system: "FRONTEND",
        traceId: "mock-trace-id",
        spanId: "mock-span-id",
      });
      expect(log.timestamp).toBeDefined();
    });

    it("should handle error logs with stack trace", () => {
      const error = new Error("test error");
      logger.error({
        message: "error occurred",
        error,
        metadata: { test: "data" },
      });

      const log = mockTransport.logs[0];
      expect(log.metadata).toMatchObject({
        test: "data",
        errorName: "Error",
        stackTrace: expect.stringContaining("test error"),
      });
    });
  });

  describe("transport handling", () => {
    it("should respect transport log level", () => {
      mockTransport.level = "warn";

      logger.info({ message: "test info", metadata: {} });
      expect(mockTransport.logs).toHaveLength(0);

      logger.warn({ message: "test warn", metadata: {} });
      expect(mockTransport.logs).toHaveLength(1);

      logger.error({ message: "test error", metadata: {} });
      expect(mockTransport.logs).toHaveLength(2);
    });

    it("should handle transport errors gracefully", async () => {
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});
      const errorTransport: Transport = {
        async log() {
          throw new Error("Transport error");
        },
      };

      logger.addTransport(errorTransport);
      await logger.info({ message: "test", metadata: {} });

      expect(consoleSpy).toHaveBeenCalledWith("Logger transport failed:", expect.any(Error));
      consoleSpy.mockRestore();
    });
  });
});
