/* eslint-disable no-console */
import { Transport } from "../transport";
import { LogEntry, LogLevel } from "../types";

type ConsoleMethod = "log" | "warn" | "error" | "debug";

interface LogStyle {
  method: ConsoleMethod;
  color: string;
}

export class ConsoleTransport implements Transport {
  public level?: LogLevel;

  private readonly logStyles: Record<LogLevel, LogStyle> = {
    debug: { method: "debug", color: "#6c757d" },
    info: { method: "log", color: "#0dcaf0" },
    warn: { method: "warn", color: "#ffc107" },
    error: { method: "error", color: "#dc3545" },
  };

  private logGroup(message: string, extraInfo: string, level: LogLevel): void {
    const style = this.logStyles[level];
    console.groupCollapsed(`%c${message}`, `color: ${style.color};`);
    console[style.method](extraInfo);
    console.trace("Stack trace");
    console.groupEnd();
  }

  public async log(entry: LogEntry): Promise<void> {
    const { message, ...rest } = entry;
    const formattedLog = JSON.stringify(rest, null, 2);

    if (entry.level) {
      this.logGroup(message, formattedLog, entry.level);
    }
  }
}

/** Signature of a logging function */
export interface LogFn {
  (message?: any, ...optionalParams: any[]): void;
}
