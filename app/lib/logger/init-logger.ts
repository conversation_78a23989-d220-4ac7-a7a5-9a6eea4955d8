import { Container } from "inversify";

import { CommonEnvService, EnvService } from "~/services/env";
import { AppConstants } from "~/utils/constants/app-constants";

import { Logger } from "./logger";
import { ConsoleTransport } from "./transports/console.transport";

export function initLogger(container: Container): void {
  const env = container.get<CommonEnvService>(EnvService);

  const logger = Logger.getInstance();
  logger.setLogLevel(env.LOG_LEVEL);
  logger.setClientNamespace(AppConstants.Namespace);
  const consoleTransport = new ConsoleTransport();
  logger.addTransport(consoleTransport);
  logger.info({ message: "Logger initialized for the client" });
}
