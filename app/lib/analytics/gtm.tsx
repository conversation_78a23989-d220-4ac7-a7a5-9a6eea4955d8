import { useNonce } from "~/context/csp-provider";
import { useInjection } from "~/hooks/use-di";
import { EnvService } from "~/services/env";

export const GoogleTagManager = () => {
  const { nonce } = useNonce();
  const envService = useInjection<EnvService>(EnvService);
  if (!envService.GOOGLE_TAG_MANAGER_ID) {
    return null;
  }
  return (
    <script
      nonce={nonce}
      dangerouslySetInnerHTML={{
        __html: `
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      window.dataLayer.push(arguments);
    }
    (function () {
        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }

        var cookiesAccepted = false;
        const cookieConsent = getCookie("cookieConsent");

        if (cookieConsent) {
            try {
                cookiesAccepted = decodeURIComponent(cookieConsent);
            } catch (e) {
                console.error("Error decoding cookie consent:", e);
            }
        }

        if (cookiesAccepted === "accepted") {
          gtag("consent", "default", {
          ad_storage: "granted",
          ad_user_data: "granted",
          ad_personalization: "granted",
          analytics_storage: "granted",
          });
          if(window && window.clarity){
            window.clarity("consent");
           }
      } else {
          gtag("consent", "default", {
            ad_storage: "denied",
            ad_user_data: "denied",
            ad_personalization: "denied",
            analytics_storage: "denied",
          });
          if(window && window.clarity){
            window.clarity("consent", false);
          } 
      }

        window.dataLayer.push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var j = document.createElement("script");
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=${envService.GOOGLE_TAG_MANAGER_ID}&l=dataLayer";
        j.onload = function () {
          try {
            const auth = localStorage.getItem("auth");
            const acc = localStorage.getItem("account");
            if (auth && acc) {
              try {
                const sub = JSON.parse(auth).state.sub;
                const email = JSON.parse(acc).state.email;
                if (sub) {
                  window.dataLayer.push({
                    user_id: sub,
                    email: email,
                    event: "setUserID",
                  });
        
                  if (window && typeof window.clarity === "function") {
                    window.clarity("identify", sub, null, null, email);
                  }
                }
              } catch (e) {
                console.error("Error parsing auth/account:", e);
              }
            }
          } catch (e) {
            // Silently handle localStorage access errors in incognito mode
            console.debug("LocalStorage not available (possibly incognito mode)");
          }
        }    
        document.head.appendChild(j);
    })();
          `,
      }}
    />
  );
};
