import { createContext, useContext } from "react";

const NonceContext = createContext<{
  nonce: string | undefined;
}>({
  nonce: undefined,
});
export const NonceProvider = ({
  children,
  nonce,
}: {
  children: React.ReactNode;
  nonce: string | undefined;
}) => {
  return (
    <NonceContext.Provider
      value={{
        nonce,
      }}
    >
      {children}
    </NonceContext.Provider>
  );
};

export const useNonce = () => {
  return useContext(NonceContext);
};

export const getNonceOnClient = () => {
  if (typeof window !== "undefined") {
    return window.__remixNonce as string;
  }
  return undefined;
};

export const NonceScript = () => {
  const { nonce } = useNonce();
  return (
    <script
      nonce={nonce}
      dangerouslySetInnerHTML={{
        __html: `window.__remixNonce = "${nonce}";`,
      }}
    />
  );
};
