import { <PERSON><PERSON><PERSON>rovider, EmotionCache, Theme<PERSON>rovider as EmotionThemeProvider } from "@emotion/react";
import { getThemeBare } from "@roshn/ui-kit";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { CssBaseline } from "~/components/css-baseline";

type ThemeProviderProps = React.PropsWithChildren<{
  cache: EmotionCache;
  darkMode?: boolean;
  rtlMode?: boolean;
}>;

export const CoreThemeProvider = ({ cache, darkMode, children }: ThemeProviderProps) => {
  const { i18n } = useTranslation();
  const locale = i18n.resolvedLanguage || "en";
  const direction = locale === "ar" ? "rtl" : "ltr";
  const [mounted, setMounted] = useState(false);
  const [device, setDevice] = useState<"mobile" | "tablet" | "desktop">("desktop");

  const detectDevice = () => {
    if (typeof window === "undefined") return "desktop";
    const width = window.innerWidth;
    if (width < 768) {
      return "mobile";
    } else if (width >= 768 && width < 1024) {
      return "tablet";
    }
    return "desktop";
  };

  useEffect(() => {
    setMounted(true);
    setDevice(detectDevice());

    const handleResize = () => {
      setDevice(detectDevice());
    };
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const theme = getThemeBare({
    direction,
    name: darkMode ? "dark" : "light",
    locale,
    device: mounted ? device : "desktop",
  });

  useEffect(() => {
    if (mounted) {
      document.documentElement.setAttribute("dir", direction);
      document.documentElement.setAttribute("lang", locale);
    }
  }, [locale, direction, mounted]);

  return (
    <CacheProvider value={cache}>
      <EmotionThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </EmotionThemeProvider>
    </CacheProvider>
  );
};
