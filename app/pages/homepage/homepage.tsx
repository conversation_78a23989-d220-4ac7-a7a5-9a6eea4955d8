import { css, useTheme } from "@emotion/react";
import { RDSButton } from "@roshn/ui-kit";
import { useEffect, useRef, useState } from "react";

interface Feature {
  title: string;
  description: string;
  icon: JSX.Element;
}

// Custom hook for intersection observer animations
function useIntersectionObserver<T extends HTMLElement = HTMLElement>(options = {}) {
  const elementRef = useRef<T | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsVisible(true);
        // Once the element is visible, we can stop observing it
        if (elementRef.current) {
          observer.unobserve(elementRef.current);
        }
      }
    }, options);

    const currentElement = elementRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [options]);

  return { ref: elementRef, isVisible };
}

const iconStyle = css`
  width: 1.5rem;
  height: 1.5rem;
`;

const features: Feature[] = [
  {
    title: "Modern Stack",
    description: "Built with Remix, React, and Emotion CSS for a modern development experience.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        css={iconStyle}
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        />
      </svg>
    ),
  },
  {
    title: "Responsive Design",
    description: "Fully responsive design that looks great on all devices, from mobile to desktop.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        css={iconStyle}
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
        />
      </svg>
    ),
  },
  {
    title: "Dark Mode & RTL",
    description:
      "Built-in dark mode and RTL direction support that automatically adapts to user preferences.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        css={iconStyle}
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
        />
      </svg>
    ),
  },
  {
    title: "TypeScript",
    description: "Full TypeScript support for a type-safe development experience.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        css={iconStyle}
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"
        />
      </svg>
    ),
  },
  {
    title: "Fast Performance",
    description: "Optimized for performance with server-side rendering and client-side hydration.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        css={iconStyle}
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 10V3L4 14h7v7l9-11h-7z"
        />
      </svg>
    ),
  },
  {
    title: "RDS Theme System",
    description: "Integrated with Roshn Design System for consistent and beautiful UI components.",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        css={iconStyle}
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
        />
      </svg>
    ),
  },
];

export default function LandingPage() {
  const theme = useTheme() as any;
  const styles = theme.rds;
  const hero = useIntersectionObserver<HTMLDivElement>({ threshold: 0.75 });
  const heroText = useIntersectionObserver<HTMLParagraphElement>({
    threshold: 0.75,
  });
  const heroButtons = useIntersectionObserver<HTMLDivElement>({
    threshold: 0.75,
  });

  // Main container styles
  const containerStyle = css`
    min-height: 100vh;
    background: linear-gradient(
      to bottom,
      ${styles.color.lightBlue[25]},
      ${styles.color.lightBlue[50]}
    );
  `;

  // Container wrapper for consistent spacing
  const sectionContainer = css`
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 ${styles.dimension[100]};
  `;

  return (
    <div css={containerStyle}>
      {/* Hero Section */}
      <section
        css={css`
          padding: ${styles.dimension[200]} 0;
          text-align: center;
        `}
      >
        <div css={sectionContainer}>
          <h1
            ref={hero.ref}
            css={css`
            ...${styles.typographies.heading["2xl"]};
            color: ${styles.color.castletonGreen[900]};
            margin-bottom: ${styles.dimension[100]};
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
            ${
              hero.isVisible &&
              `
              opacity: 1;
              transform: translateY(0);
            `
            }
          `}
          >
            Welcome to Roshn Boilerplate
          </h1>
          <p
            ref={heroText.ref}
            css={css`
            ...${styles.typographies.body.lg};
            color: ${styles.color.castletonGreen[700]};
            max-width: 600px;
            margin: 0 auto ${styles.dimension[100]};
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out 0.2s, transform 0.6s ease-out 0.2s;
            ${
              heroText.isVisible &&
              `
              opacity: 1;
              transform: translateY(0);
            `
            }
          `}
          >
            A modern, responsive, and feature-rich boilerplate for building web applications with
            Remix, React, and Emotion CSS.
          </p>
          <div
            ref={heroButtons.ref}
            css={css`
              display: flex;
              gap: ${styles.dimension[50]};
              justify-content: center;
              opacity: 0;
              transform: translateY(20px);
              transition:
                opacity 0.6s ease-out 0.4s,
                transform 0.6s ease-out 0.4s;
              ${heroButtons.isVisible &&
              `
              opacity: 1;
              transform: translateY(0);
            `}
            `}
          >
            <RDSButton variant="primary" size="lg" text="Get Started" />
            <RDSButton variant="secondary" size="lg" text="Learn More" />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section
        css={css`
          padding: ${styles.dimension[200]} 0;
          background-color: ${styles.color.lightBlue[25]};
        `}
      >
        <div css={sectionContainer}>
          <h2
            css={css`
            ...${styles.typographies.heading.xl};
            color: ${styles.color.castletonGreen[900]};
            text-align: center;
            margin-bottom: ${styles.dimension[100]};
          `}
          >
            Features
          </h2>
          <div
            css={css`
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
              gap: ${styles.dimension[100]};
            `}
          >
            {features.map((feature, index) => (
              <div
                key={index}
                css={css`
                  padding: ${styles.dimension[100]};
                  background-color: ${styles.color.background.ui.primary.default};
                  border-radius: ${styles.borderRadius["lg"]};
                  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                  transition: transform 0.2s ease-in-out;

                  &:hover {
                    transform: translateY(-4px);
                    background-color: ${styles.color.background.ui.primary.hover};
                  }
                  &:active {
                    transform: translateY(-4px);
                    background-color: ${styles.color.background.ui.primary.pressed};
                  }
                `}
              >
                <div
                  css={css`
                    color: ${styles.color.lightBlue[500]};
                    margin-bottom: ${styles.dimension[50]};
                  `}
                >
                  {feature.icon}
                </div>
                <h3
                  css={css`
                  ...${styles.typographies.heading.lg};
                  color: ${styles.color.castletonGreen[900]};
                  margin-bottom: ${styles.dimension[50]};
                `}
                >
                  {feature.title}
                </h3>
                <p
                  css={css`
                  ...${styles.typographies.body.md};
                  color: ${styles.color.castletonGreen[700]};
                `}
                >
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
