[![Commitizen friendly](https://img.shields.io/badge/commitizen-friendly-brightgreen.svg)](http://commitizen.github.io/cz-cli/)

# ROSHN Brokerage Web Frontend

Welcome to the ROSHN Brokerage Web Frontend!

- 📖 [Remix docs](https://remix.run/docs)

## Development

To get started, clone the repository and install the dependencies:

```shellscript
git clone https://gitlab.com/ROSHN.com/roshn-com-application/frontend/roshn-web-brokerage-frontend.git
cd roshn-web-brokerage-frontend
pnpm install
```

Then, run the dev server:

```shellscript
pnpm run dev
```

## Deployment

First, build your app for production:

```sh
pnpm run build
```

Then run the app in production mode:

```sh
pnpm start
```

Now you'll need to pick a host to deploy it to.

### DIY

If you're familiar with deploying Node applications, the built-in Remix app server is production-ready.

Make sure to deploy the output of `pnpm run build`

- `build/server`
- `build/client`

## Documentation

For more detailed information about the project's architecture and testing strategy, please see the following guides:

- [**Architecture Guide**](docs/ARCHITECTURE.md) - Includes detailed documentation about:
  - Dependency Injection
  - Services
  - Data Fetching
  - Routing
  - Form Handling
  - API Mocking
  - [**Logging System**](docs/ARCHITECTURE.md#logging-system)
- [**End-to-End (E2E) Testing Guide**](docs/E2E_TESTING.md)

## Contributing

We welcome contributions from the community! Please read our [Contributing Guide](CONTRIBUTING.md) to learn about our
development process, how to propose bugfixes and improvements, and how to build and test your changes.

Also, be sure to review our [Code of Conduct](CODE_OF_CONDUCT.md). We are committed to providing a safe and welcoming
environment for everyone.

## Styling

This template comes with [Tailwind CSS](https://tailwindcss.com/) already configured for a simple default starting
experience. You can use whatever css framework you prefer. See the
[Vite docs on css](https://vitejs.dev/guide/features.html#css) for more information.
